-- WEBHOOKS SEED
-- PLEASE NOTE: These webhooks are only for development purposes. Leave them as they are or add new ones.

-- These webhooks are only for development purposes.
-- In production, you should manually create webhooks in the Supabase dashboard (or create a migration to do so).
-- We don't do it because you'll need to manually add your webhook URL and secret key.

-- this webhook will be triggered after deleting an account
create trigger "accounts_teardown"
    after delete
    on "public"."accounts"
    for each row
execute function "supabase_functions"."http_request"(
        'http://*************:3000/api/db/webhook',
        'POST',
        '{"Content-Type":"application/json", "X-Supabase-Event-Signature":"WEBHOOKSECRET"}',
        '{}',
        '5000'
                 );

-- this webhook will be triggered after a delete on the subscriptions table
-- which should happen when a user deletes their account (and all their subscriptions)
create trigger "subscriptions_delete"
    after delete
    on "public"."subscriptions"
    for each row
execute function "supabase_functions"."http_request"(
        'http://*************:3000/api/db/webhook',
        'POST',
        '{"Content-Type":"application/json", "X-Supabase-Event-Signature":"WEBHOOKSECRET"}',
        '{}',
        '5000'
                 );

-- this webhook will be triggered after every insert on the invitations table
-- which should happen when a user invites someone to their account
create trigger "invitations_insert"
    after insert
    on "public"."invitations"
    for each row
execute function "supabase_functions"."http_request"(
        'http://*************:3000/api/db/webhook',
        'POST',
        '{"Content-Type":"application/json", "X-Supabase-Event-Signature":"WEBHOOKSECRET"}',
        '{}',
        '5000'
                 );


-- DATA SEED
-- This is a data dump for testing purposes. It should be used to seed the database with data for testing.


--
-- Data for Name: flow_state; Type: TABLE DATA; Schema: auth; Owner: supabase_auth_admin
--


--
-- Data for Name: users; Type: TABLE DATA; Schema: auth; Owner: supabase_auth_admin
--

INSERT INTO "auth"."users" ("instance_id", "id", "aud", "role", "email", "encrypted_password", "email_confirmed_at",
                            "invited_at", "confirmation_token", "confirmation_sent_at", "recovery_token",
                            "recovery_sent_at", "email_change_token_new", "email_change", "email_change_sent_at",
                            "last_sign_in_at", "raw_app_meta_data", "raw_user_meta_data", "is_super_admin",
                            "created_at", "updated_at", "phone", "phone_confirmed_at", "phone_change",
                            "phone_change_token", "phone_change_sent_at", "email_change_token_current",
                            "email_change_confirm_status", "banned_until", "reauthentication_token",
                            "reauthentication_sent_at", "is_sso_user", "deleted_at", "is_anonymous")
VALUES ('00000000-0000-0000-0000-000000000000', 'b73eb03e-fb7a-424d-84ff-18e2791ce0b4', 'authenticated',
        'authenticated', '<EMAIL>', '$2a$10$b3ZPpU6TU3or30QzrXnZDuATPAx2pPq3JW.sNaneVY3aafMSuR4yi',
        '2024-04-20 08:38:00.860548+00', NULL, '', '2024-04-20 08:37:43.343769+00', '', NULL, '', '', NULL,
        '2024-04-20 08:38:00.93864+00', '{"provider": "email", "providers": ["email"]}',
        '{"sub": "b73eb03e-fb7a-424d-84ff-18e2791ce0b4", "email": "<EMAIL>", "email_verified": false, "phone_verified": false}',
        NULL, '2024-04-20 08:37:43.3385+00', '2024-04-20 08:38:00.942809+00', NULL, NULL, '', '', NULL, '', 0, NULL, '',
        NULL, false, NULL, false),
       ('00000000-0000-0000-0000-000000000000', '31a03e74-1639-45b6-bfa7-77447f1a4762', 'authenticated',
        'authenticated', '<EMAIL>', '$2a$10$NaMVRrI7NyfwP.AfAVWt6O/abulGnf9BBqwa6DqdMwXMvOCGpAnVO',
        '2024-04-20 08:20:38.165331+00', NULL, '', NULL, '', NULL, '', '', NULL, '2024-04-20 09:36:02.521776+00',
        '{"provider": "email", "providers": ["email"], "role": "super-admin"}',
        '{"sub": "31a03e74-1639-45b6-bfa7-77447f1a4762", "email": "<EMAIL>", "email_verified": false, "phone_verified": false}',
        NULL, '2024-04-20 08:20:34.459113+00', '2024-04-20 10:07:48.554125+00', NULL, NULL, '', '', NULL, '', 0, NULL,
        '', NULL, false, NULL, false),
       ('00000000-0000-0000-0000-000000000000', '5c064f1b-78ee-4e1c-ac3b-e99aa97c99bf', 'authenticated',
        'authenticated', '<EMAIL>', '$2a$10$D6arGxWJShy8q4RTW18z7eW0vEm2hOxEUovUCj5f3NblyHfamm5/a',
        '2024-04-20 08:36:37.517993+00', NULL, '', '2024-04-20 08:36:27.639648+00', '', NULL, '', '', NULL,
        '2024-04-20 08:36:37.614337+00', '{"provider": "email", "providers": ["email"]}',
        '{"sub": "5c064f1b-78ee-4e1c-ac3b-e99aa97c99bf", "email": "<EMAIL>", "email_verified": false, "phone_verified": false}',
        NULL, '2024-04-20 08:36:27.630379+00', '2024-04-20 08:36:37.617955+00', NULL, NULL, '', '', NULL, '', 0, NULL,
        '', NULL, false, NULL, false),
       ('00000000-0000-0000-0000-000000000000', '6b83d656-e4ab-48e3-a062-c0c54a427368', 'authenticated',
        'authenticated', '<EMAIL>', '$2a$10$6h/x.AX.6zzphTfDXIJMzuYx13hIYEi/Iods9FXH19J2VxhsLycfa',
        '2024-04-20 08:41:15.376778+00', NULL, '', '2024-04-20 08:41:08.689674+00', '', NULL, '', '', NULL,
        '2024-04-20 08:41:15.484606+00', '{"provider": "email", "providers": ["email"]}',
        '{"sub": "6b83d656-e4ab-48e3-a062-c0c54a427368", "email": "<EMAIL>", "email_verified": false, "phone_verified": false}',
        NULL, '2024-04-20 08:41:08.683395+00', '2024-04-20 08:41:15.485494+00', NULL, NULL, '', '', NULL, '', 0, NULL,
        '', NULL, false, NULL, false),
       ('00000000-0000-0000-0000-000000000000', 'c5b930c9-0a76-412e-a836-4bc4849a3270', 'authenticated',
        'authenticated', '<EMAIL>',
        '$2a$10$gzxQw3vaVni8Ke9UVcn6ueWh674.6xImf6/yWYNc23BSeYdE9wmki', '2025-02-24 13:25:11.176987+00', null, '',
        '2025-02-24 13:25:01.649714+00', '', null, '', '', null, '2025-02-24 13:25:11.17957+00',
        '{"provider": "email", "providers": ["email"], "role": "super-admin"}',
        '{"sub": "c5b930c9-0a76-412e-a836-4bc4849a3270", "email": "<EMAIL>", "email_verified": true, "phone_verified": false}',
        null, '2025-02-24 13:25:01.646641+00', '2025-02-24 13:25:11.181332+00', null, null, '', '', null
           , '', '0', null, '', null, 'false', null, 'false');

--
-- Data for Name: identities; Type: TABLE DATA; Schema: auth; Owner: supabase_auth_admin
--

INSERT INTO "auth"."identities" ("provider_id", "user_id", "identity_data", "provider", "last_sign_in_at", "created_at",
                                 "updated_at", "id")
VALUES ('31a03e74-1639-45b6-bfa7-77447f1a4762', '31a03e74-1639-45b6-bfa7-77447f1a4762',
        '{"sub": "31a03e74-1639-45b6-bfa7-77447f1a4762", "email": "<EMAIL>", "email_verified": false, "phone_verified": false}',
        'email', '2024-04-20 08:20:34.46275+00', '2024-04-20 08:20:34.462773+00', '2024-04-20 08:20:34.462773+00',
        '9bb58bad-24a4-41a8-9742-1b5b4e2d8abd'),
       ('5c064f1b-78ee-4e1c-ac3b-e99aa97c99bf', '5c064f1b-78ee-4e1c-ac3b-e99aa97c99bf',
        '{"sub": "5c064f1b-78ee-4e1c-ac3b-e99aa97c99bf", "email": "<EMAIL>", "email_verified": false, "phone_verified": false}',
        'email', '2024-04-20 08:36:27.637388+00', '2024-04-20 08:36:27.637409+00', '2024-04-20 08:36:27.637409+00',
        '090598a1-ebba-4879-bbe3-38d517d5066f'),
       ('b73eb03e-fb7a-424d-84ff-18e2791ce0b4', 'b73eb03e-fb7a-424d-84ff-18e2791ce0b4',
        '{"sub": "b73eb03e-fb7a-424d-84ff-18e2791ce0b4", "email": "<EMAIL>", "email_verified": false, "phone_verified": false}',
        'email', '2024-04-20 08:37:43.342194+00', '2024-04-20 08:37:43.342218+00', '2024-04-20 08:37:43.342218+00',
        '4392e228-a6d8-4295-a7d6-baed50c33e7c'),
       ('6b83d656-e4ab-48e3-a062-c0c54a427368', '6b83d656-e4ab-48e3-a062-c0c54a427368',
        '{"sub": "6b83d656-e4ab-48e3-a062-c0c54a427368", "email": "<EMAIL>", "email_verified": false, "phone_verified": false}',
        'email', '2024-04-20 08:41:08.687948+00', '2024-04-20 08:41:08.687982+00', '2024-04-20 08:41:08.687982+00',
        'd122aca5-4f29-43f0-b1b1-940b000638db'),
        ('c5b930c9-0a76-412e-a836-4bc4849a3270', 'c5b930c9-0a76-412e-a836-4bc4849a3270',
        '{"sub": "c5b930c9-0a76-412e-a836-4bc4849a3270", "email": "<EMAIL>", "email_verified": true, "phone_verified": false}',
        'email', '2025-02-24 13:25:01.646641+00', '2025-02-24 13:25:11.181332+00', '2025-02-24 13:25:11.181332+00',
        'c5b930c9-0a76-412e-a836-4bc4849a3270');

--
-- Data for Name: instances; Type: TABLE DATA; Schema: auth; Owner: supabase_auth_admin
--


--
-- Data for Name: sessions; Type: TABLE DATA; Schema: auth; Owner: supabase_auth_admin
--

--
-- Data for Name: mfa_amr_claims; Type: TABLE DATA; Schema: auth; Owner: supabase_auth_admin
--


--
-- Data for Name: mfa_factors; Type: TABLE DATA; Schema: auth; Owner: supabase_auth_admin
--


--
-- Data for Name: mfa_challenges; Type: TABLE DATA; Schema: auth; Owner: supabase_auth_admin
--


--
-- Data for Name: refresh_tokens; Type: TABLE DATA; Schema: auth; Owner: supabase_auth_admin
--

--
-- Data for Name: sso_providers; Type: TABLE DATA; Schema: auth; Owner: supabase_auth_admin
--


--
-- Data for Name: saml_providers; Type: TABLE DATA; Schema: auth; Owner: supabase_auth_admin
--


--
-- Data for Name: saml_relay_states; Type: TABLE DATA; Schema: auth; Owner: supabase_auth_admin
--


--
-- Data for Name: sso_domains; Type: TABLE DATA; Schema: auth; Owner: supabase_auth_admin
--


--
-- Data for Name: key; Type: TABLE DATA; Schema: pgsodium; Owner: supabase_admin
--


--
-- Data for Name: accounts; Type: TABLE DATA; Schema: public; Owner: postgres
--

INSERT INTO "public"."accounts" ("id", "primary_owner_user_id", "name", "slug", "email", "is_personal_account",
                                 "updated_at", "created_at", "created_by", "updated_by", "picture_url", "public_data")
VALUES ('5deaa894-2094-4da3-b4fd-1fada0809d1c', '31a03e74-1639-45b6-bfa7-77447f1a4762', 'Makerkit', 'makerkit', NULL,
        false, NULL, NULL, NULL, NULL, NULL, '{}');

--
-- Data for Name: roles; Type: TABLE DATA; Schema: public; Owner: postgres
--

INSERT INTO "public"."roles" ("name", "hierarchy_level")
VALUES ('custom-role', 4);

--
-- Data for Name: accounts_memberships; Type: TABLE DATA; Schema: public; Owner: postgres
--

INSERT INTO "public"."accounts_memberships" ("user_id", "account_id", "account_role", "created_at", "updated_at",
                                             "created_by", "updated_by")
VALUES ('31a03e74-1639-45b6-bfa7-77447f1a4762', '5deaa894-2094-4da3-b4fd-1fada0809d1c', 'owner',
        '2024-04-20 08:21:16.802867+00', '2024-04-20 08:21:16.802867+00', NULL, NULL),
       ('5c064f1b-78ee-4e1c-ac3b-e99aa97c99bf', '5deaa894-2094-4da3-b4fd-1fada0809d1c', 'owner',
        '2024-04-20 08:36:44.21028+00', '2024-04-20 08:36:44.21028+00', NULL, NULL),
       ('b73eb03e-fb7a-424d-84ff-18e2791ce0b4', '5deaa894-2094-4da3-b4fd-1fada0809d1c', 'custom-role',
        '2024-04-20 08:38:02.50993+00', '2024-04-20 08:38:02.50993+00', NULL, NULL),
       ('6b83d656-e4ab-48e3-a062-c0c54a427368', '5deaa894-2094-4da3-b4fd-1fada0809d1c', 'member',
        '2024-04-20 08:41:17.833709+00', '2024-04-20 08:41:17.833709+00', NULL, NULL);

-- MFA Factors
INSERT INTO "auth"."mfa_factors" ("id", "user_id", "friendly_name", "factor_type", "status", "created_at", "updated_at",
                                  "secret", "phone", "last_challenged_at")
VALUES ('659e3b57-1128-4d26-8757-f714fd073fc4', 'c5b930c9-0a76-412e-a836-4bc4849a3270', 'iPhone', 'totp', 'verified',
        '2025-02-24 13:23:55.5805+00', '2025-02-24 13:24:32.591999+00', 'NHOHJVGPO3R3LKVPRMNIYLCDMBHUM2SE', null,
        '2025-02-24 13:24:32.563314+00');

--
-- Data for Name: billing_customers; Type: TABLE DATA; Schema: public; Owner: postgres
--


--
-- Data for Name: invitations; Type: TABLE DATA; Schema: public; Owner: postgres
--


--
-- Data for Name: orders; Type: TABLE DATA; Schema: public; Owner: postgres
--


--
-- Data for Name: order_items; Type: TABLE DATA; Schema: public; Owner: postgres
--


--
-- Data for Name: subscriptions; Type: TABLE DATA; Schema: public; Owner: postgres
--


--
-- Data for Name: subscription_items; Type: TABLE DATA; Schema: public; Owner: postgres
--


--
-- Data for Name: buckets; Type: TABLE DATA; Schema: storage; Owner: supabase_storage_admin
--

--
-- Data for Name: objects; Type: TABLE DATA; Schema: storage; Owner: supabase_storage_admin
--


--
-- Data for Name: s3_multipart_uploads; Type: TABLE DATA; Schema: storage; Owner: supabase_storage_admin
--


--
-- Data for Name: s3_multipart_uploads_parts; Type: TABLE DATA; Schema: storage; Owner: supabase_storage_admin
--


--
-- Data for Name: hooks; Type: TABLE DATA; Schema: supabase_functions; Owner: supabase_functions_admin
--

--
-- Data for Name: secrets; Type: TABLE DATA; Schema: vault; Owner: supabase_admin
--

--
-- Name: refresh_tokens_id_seq; Type: SEQUENCE SET; Schema: auth; Owner: supabase_auth_admin
--

SELECT pg_catalog.setval('"auth"."refresh_tokens_id_seq"', 5, true);


--
-- Name: billing_customers_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('"public"."billing_customers_id_seq"', 1, false);


--
-- Name: invitations_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('"public"."invitations_id_seq"', 19, true);


--
-- Name: role_permissions_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('"public"."role_permissions_id_seq"', 7, true);


--
-- Name: hooks_id_seq; Type: SEQUENCE SET; Schema: supabase_functions; Owner: supabase_functions_admin
--

SELECT pg_catalog.setval('"supabase_functions"."hooks_id_seq"', 19, true);

--
-- Data for Name: comfyui_backends; Type: TABLE DATA; Schema: public; Owner: postgres
--

INSERT INTO "public"."comfyui_backends" ("id", "name", "display_name", "description", "endpoint_url", "api_key", "timeout_seconds", "status", "priority", "max_concurrent_jobs", "gpu_memory_gb", "cpu_cores", "supported_models", "last_health_check", "health_check_interval_seconds", "consecutive_failures", "total_jobs_completed", "total_execution_time_seconds", "average_execution_time_seconds", "config_options", "created_at", "updated_at")
VALUES 
  ('11111111-1111-1111-1111-111111111111', 'sa-a800-01', 'SA A800 服务器 01', 'A800 GPU服务器 - 端口8188', 'http://*************:8188', NULL, 300, 'active', 1, 2, 80, 64, '{"sdxl", "sd15", "flux"}', NOW(), 60, 0, 0, 0, NULL, '{}', NOW(), NOW()),
  ('22222222-2222-2222-2222-222222222222', 'sa-a800-02', 'SA A800 服务器 02', 'A800 GPU服务器 - 端口8288', 'http://*************:8288', NULL, 300, 'active', 2, 2, 80, 64, '{"sdxl", "sd15", "flux"}', NOW(), 60, 0, 0, 0, NULL, '{}', NOW(), NOW()),
  ('33333333-3333-3333-3333-333333333333', 'sa-a800-03', 'SA A800 服务器 03', 'A800 GPU服务器 - 端口8388', 'http://*************:8388', NULL, 300, 'active', 3, 2, 80, 64, '{"sdxl", "sd15", "flux"}', NOW(), 60, 0, 0, 0, NULL, '{}', NOW(), NOW()),
  ('44444444-4444-4444-4444-**********44', 'sa-a800-04', 'SA A800 服务器 04', 'A800 GPU服务器 - 端口8488', 'http://*************:8488', NULL, 300, 'active', 4, 2, 80, 64, '{"sdxl", "sd15", "flux"}', NOW(), 60, 0, 0, 0, NULL, '{}', NOW(), NOW()),
  ('55555555-5555-5555-5555-555555555555', 'sa-a800-05', 'SA A800 服务器 05', 'A800 GPU服务器 - 端口8588', 'http://*************:8588', NULL, 300, 'active', 5, 2, 80, 64, '{"sdxl", "sd15", "flux"}', NOW(), 60, 0, 0, 0, NULL, '{}', NOW(), NOW()),
  ('66666666-6666-6666-6666-666666666666', 'sa-a800-06', 'SA A800 服务器 06', 'A800 GPU服务器 - 端口8688', 'http://*************:8688', NULL, 300, 'active', 6, 2, 80, 64, '{"sdxl", "sd15", "flux"}', NOW(), 60, 0, 0, 0, NULL, '{}', NOW(), NOW()),
  ('77777777-7777-7777-7777-777777777777', 'sa-a800-07', 'SA A800 服务器 07', 'A800 GPU服务器 - 端口8788', 'http://*************:8788', NULL, 300, 'active', 7, 2, 80, 64, '{"sdxl", "sd15", "flux"}', NOW(), 60, 0, 0, 0, NULL, '{}', NOW(), NOW()),
  ('88888888-8888-8888-8888-888888888888', 'sa-a800-08', 'SA A800 服务器 08', 'A800 GPU服务器 - 端口8888', 'http://*************:8888', NULL, 300, 'active', 8, 2, 80, 64, '{"sdxl", "sd15", "flux"}', NOW(), 60, 0, 0, 0, NULL, '{}', NOW(), NOW());
--
-- Data for Name: comfyui_workflows; Type: TABLE DATA; Schema: public; Owner: postgres
--

INSERT INTO "public"."comfyui_workflows" ("id", "name", "display_name", "description", "workflow_type", "workflow_json", "version", "author_id", "is_public", "tags", "created_at", "updated_at") VALUES
	('4999dde9-2328-4636-8079-72363e4ea980', 'test2', 'test', NULL, 'txt2img', '{"3": {"_meta": {"title": "K采样器"}, "inputs": {"cfg": 7, "seed": 1383541548955, "model": ["4", 0], "steps": 30, "denoise": 1, "negative": ["7", 0], "positive": ["6", 0], "scheduler": "karras", "latent_image": ["71", 0], "sampler_name": "euler_ancestral"}, "class_type": "KSampler"}, "4": {"_meta": {"title": "Checkpoint加载器（简易）"}, "inputs": {"ckpt_name": "checkpoint/wai-nsfw-illustrious-sdxl/illustrious/1761560-wainsfwillustrious_v140/1761560_waiNSFWIllustrious_v140.safetensors"}, "class_type": "CheckpointLoaderSimple"}, "6": {"_meta": {"title": "Positive CLIP"}, "inputs": {"clip": ["75", 0], "text": ["48", 0]}, "class_type": "CLIPTextEncode"}, "7": {"_meta": {"title": "Negative CLIP"}, "inputs": {"clip": ["75", 0], "text": "low quality, worst quality, text, signature, jpeg artifacts, bad anatomy, old, early, copyright name, watermark, artist name, signature, weibo username, mosaic censoring, bar censor, censored, animals"}, "class_type": "CLIPTextEncode"}, "8": {"_meta": {"title": "VAE解码"}, "inputs": {"vae": ["4", 2], "samples": ["3", 0]}, "class_type": "VAEDecode"}, "38": {"_meta": {"title": "TIPO"}, "inputs": {"seed": 1383541548955, "tags": ["73", 0], "min_p": 0.05, "top_k": 100, "top_p": 1, "width": 1344, "device": "cuda", "format": "<|special|>, \n<|characters|>, <|copyrights|>, \n<|artist|>, \n\n<|general|>\n\n<|extended|>\n\n<|quality|>, <|meta|>, <|rating|>", "height": 896, "ban_tags": "weapon, illustration, gun, ball, censor, tape, text, speeach, animal, name, sign, bondage, rope, multiple, heterochromia, open mouth, meme, sticker, dragon, from behind", "nl_length": "short", "nl_prompt": "", "tag_length": "long", "tipo_model": "KBlueLeaf/TIPO-500M-ft", "temperature": 0.5}, "class_type": "TIPO"}, "45": {"_meta": {"title": "TIPO Generated Prompt"}, "inputs": {"images": ["8", 0], "filename_prefix": "ComfyUI"}, "class_type": "SaveImage"}, "48": {"_meta": {"title": "Formatted TIPO Output"}, "inputs": {"text": ["38", 0], "text2": "1girl, \n\n(fkey:0.5), (fuzichoco:0.6), \n\n(artist:gurande \\(g-size\\):0.75), artist:ningen mame, artist:sho, sho lwlw, (artist:rhasta:0.9), (artist:wlop:0.7), (artist:ke-ta:0.6), artist:chen bin, smile, open mouth, simple background, white background, hat, twintails, monochrome, :d, drill hair, eyepatch, lolita fashion, top hat, mini hat, mini top hat, medical eyepatch, chainsaw, dress, gothic lolita, greyscale, ahoge, ribbon, looking at viewer, holding, solo, short hair, long sleeves, frill\n\nA young girl with shoulder-length blonde hair styled in two pigtails. she is wearing a black jacket with a ruffled collar and a bow on the left side of her head. the girl has a big smile on her face and is looking directly at the camera"}, "class_type": "ShowText|pysssss"}, "71": {"_meta": {"title": "空Latent图像"}, "inputs": {"width": 1344, "height": 896, "batch_size": 1}, "class_type": "EmptyLatentImage"}, "72": {"_meta": {"title": "画师串"}, "inputs": {"text": "(artist:gurande \\(g-size\\):0.75), (artist:ningen_mame:0.9),(artist:sho_(sho_lwlw):0.9),(artist:rhasta:0.9),(artist:wlop:0.7),(artist:ke-ta:0.6),(fkey:0.5),(fuzichoco:0.6),artist:chen bin,"}, "class_type": "Text Multiline"}, "73": {"_meta": {"title": "Text Concatenate"}, "inputs": {"text_a": ["72", 0], "text_b": ["74", 0], "delimiter": ", ", "clean_whitespace": "true"}, "class_type": "Text Concatenate"}, "74": {"_meta": {"title": "正面提示词"}, "inputs": {"text": "{prompt}"}, "class_type": "Text Multiline"}, "75": {"_meta": {"title": "设置CLIP最后一层"}, "inputs": {"clip": ["4", 1], "stop_at_clip_layer": -2}, "class_type": "CLIPSetLastLayer"}}', '1.0.0', 'c5b930c9-0a76-412e-a836-4bc4849a3270', true, '{}', '2025-07-01 15:02:58.060192+00', '2025-07-01 15:02:58.060192+00'),
	('9b839587-7baa-48a2-91da-14981c7dc984', 'flux-base', 'flux-base', NULL, 'txt2img', '{"6": {"_meta": {"title": "CLIP Text Encode (Positive Prompt)"}, "inputs": {"clip": ["44", 0], "text": "This is a highly detailed, photorealistic CGI image of a young, ethereal woman standing in a grand, gothic-style cathedral. The subject, likely a fantasy character, is depicted with delicate features, pointed ears, and long, flowing blonde hair styled in intricate braids adorned with small, shimmering ornaments. Her skin has a pale, porcelain tone, and her large, expressive eyes are a captivating shade of blue.\n\nShe wears a stunning, ornate outfit that blends medieval and fantasy elements. The ensemble features a fitted, sleeveless black and white bodice with intricate gold embroidery and a silver chain belt. Over this, a sheer, flowing black and blue cape with a gradient effect from dark to light blue, and adorned with gold floral patterns, cascades behind her. The cape''s edges are intricately embroidered with gold and blue thread, creating a sense of movement and fluidity.\n\nThe character''s feet are bare, except for delicate, sparkling silver anklets. She stands poised, with her arms extended to either side, as if embracing the grandeur of her surroundings. Her expression is serene, with a hint of mystique.\n\nThe background is a majestic cathedral, with tall, arched stained-glass windows that filter a soft, blue light into the scene, casting an ethereal glow on the subject. The architecture is ornate, with gothic-style columns, intricate carvings, and a vaulted ceiling. The overall atmosphere is one of otherworldly beauty and tranquility, enhanced by the soft, diffused lighting and the subject''s enchanting presence."}, "class_type": "CLIPTextEncode"}, "8": {"_meta": {"title": "VAE解码"}, "inputs": {"vae": ["10", 0], "samples": ["13", 0]}, "class_type": "VAEDecode"}, "9": {"_meta": {"title": "保存图像"}, "inputs": {"images": ["64", 0], "filename_prefix": "ComfyUI"}, "class_type": "SaveImage"}, "10": {"_meta": {"title": "加载VAE"}, "inputs": {"vae_name": "ae.safetensors"}, "class_type": "VAELoader"}, "13": {"_meta": {"title": "自定义采样器（高级）"}, "inputs": {"noise": ["25", 0], "guider": ["22", 0], "sigmas": ["17", 0], "sampler": ["16", 0], "latent_image": ["27", 0]}, "class_type": "SamplerCustomAdvanced"}, "16": {"_meta": {"title": "K采样器选择"}, "inputs": {"sampler_name": "euler"}, "class_type": "KSamplerSelect"}, "17": {"_meta": {"title": "基本调度器"}, "inputs": {"model": ["30", 0], "steps": 20, "denoise": 1, "scheduler": "simple"}, "class_type": "BasicScheduler"}, "22": {"_meta": {"title": "基本引导器"}, "inputs": {"model": ["30", 0], "conditioning": ["26", 0]}, "class_type": "BasicGuider"}, "25": {"_meta": {"title": "随机噪波"}, "inputs": {"noise_seed": 995508786866886}, "class_type": "RandomNoise"}, "26": {"_meta": {"title": "Flux引导"}, "inputs": {"guidance": 3.5, "conditioning": ["6", 0]}, "class_type": "FluxGuidance"}, "27": {"_meta": {"title": "空Latent图像（SD3）"}, "inputs": {"width": ["54", 0], "height": ["55", 0], "batch_size": 1}, "class_type": "EmptySD3LatentImage"}, "30": {"_meta": {"title": "采样算法（Flux）"}, "inputs": {"model": ["58", 0], "width": ["54", 0], "height": ["55", 0], "max_shift": 1.15, "base_shift": 0.5}, "class_type": "ModelSamplingFlux"}, "44": {"_meta": {"title": "Nunchaku Text Encoder Loader (Deprecated)"}, "inputs": {"int4_model": "none", "model_type": "flux", "use_4bit_t5": "disable", "t5_min_length": 512, "text_encoder1": "t5/t5xxl_fp16.safetensors", "text_encoder2": "clip_l.safetensors"}, "class_type": "NunchakuTextEncoderLoader"}, "45": {"_meta": {"title": "Nunchaku FLUX DiT Loader"}, "inputs": {"i2f_mode": "enabled", "attention": "nunchaku-fp16", "data_type": "bfloat16", "device_id": 0, "model_path": "svdq-int4-flux.1-dev", "cpu_offload": "auto", "cache_threshold": 0}, "class_type": "NunchakuFluxDiTLoader"}, "54": {"_meta": {"title": "width"}, "inputs": {"value": 768}, "class_type": "INTConstant"}, "55": {"_meta": {"title": "height"}, "inputs": {"value": 1536}, "class_type": "INTConstant"}, "58": {"_meta": {"title": "Nunchaku FLUX.1 LoRA Loader"}, "inputs": {"model": ["45", 0], "lora_name": "ZOZ_芙露德莉斯-大卡提希娅_极致逼真人像.safetensors", "lora_strength": 0.8000000000000002}, "class_type": "NunchakuFluxLoraLoader"}, "62": {"_meta": {"title": "Number Operation"}, "inputs": {"number_a": ["67", 0], "number_b": ["63", 0], "operation": "division"}, "class_type": "Number Operation"}, "63": {"_meta": {"title": "Text to Number"}, "inputs": {"text": ["68", 0]}, "class_type": "Text to Number"}, "64": {"_meta": {"title": "Upscale Image By"}, "inputs": {"image": ["65", 0], "scale_by": ["62", 1], "upscale_method": "nearest-exact"}, "class_type": "ImageScaleBy"}, "65": {"_meta": {"title": "Upscale Image (using Model)"}, "inputs": {"image": ["66", 0], "upscale_model": ["71", 0]}, "class_type": "ImageUpscaleWithModel"}, "66": {"_meta": {"title": "限制分辨率"}, "inputs": {"mode": true, "size": 1216, "images": ["8", 0]}, "class_type": "easy imageScaleDownToSize"}, "67": {"_meta": {"title": "Text to Number"}, "inputs": {"text": ["69", 0]}, "class_type": "Text to Number"}, "68": {"_meta": {"title": "Text Multiline"}, "inputs": {"text": "4"}, "class_type": "Text Multiline"}, "69": {"_meta": {"title": "超分倍率"}, "inputs": {"text": "3"}, "class_type": "Text Multiline"}, "71": {"_meta": {"title": "Load Upscale Model"}, "inputs": {"model_name": "RealESRGAN_x4plus.pth"}, "class_type": "UpscaleModelLoader"}}', '1.0.0', 'c5b930c9-0a76-412e-a836-4bc4849a3270', true, '{}', '2025-07-02 13:31:20.704754+00', '2025-07-02 13:31:20.704754+00'),
	('3ba5bdd2-fc29-41d3-8eac-9ef5e31e08f6', 'kokoroTTSbase', 'kokoroTTSbase', NULL, 'tts', '{"22": {"_meta": {"title": "Multi Line Text"}, "inputs": {"multi_line_prompt": "女子手中的剑指着淑月的咽喉\n再进一分便可入肉\n符不离觉得\n那剑好看的厉害\n细薄柔韧\n遍体银光\n剑身上镶着蓝色的凤凰\n精致得宛若下一刻就能飞起来\n这样复杂的装饰会增加刺进去以及拔出来的难度\n就实用性而言\n这种东西用来刺人不如摆在墙上用来装饰\n但握在这样的女子手里\n却又无端的让人觉得\n美好"}, "class_type": "MultiLinePromptKK"}, "40": {"_meta": {"title": "Kokoro ZH Run"}, "inputs": {"text": ["22", 0], "speed": 1.2000000000000002, "voice": "zf_001.pt", "voice_s2": "zf_002.pt", "unload_model": false, "enable_dialogue": false}, "class_type": "KokoroZHRun"}, "45": {"_meta": {"title": "保存音频"}, "inputs": {"audio": ["40", 0], "audioUI": "", "filename_prefix": "audio/ComfyUI"}, "class_type": "SaveAudio"}}', '1.0.0', 'c5b930c9-0a76-412e-a836-4bc4849a3270', true, '{}', '2025-07-04 09:20:11.806879+00', '2025-07-04 09:30:25.640724+00');


--
-- Data for Name: comfyui_workflow_configs; Type: TABLE DATA; Schema: public; Owner: postgres
--

INSERT INTO "public"."comfyui_workflow_configs" ("id", "workflow_id", "config_name", "display_name", "description", "node_overrides", "default_parameters", "parameter_mapping", "parameter_limits", "required_parameters", "status", "is_featured", "is_public", "display_order", "estimated_time_seconds", "gpu_memory_mb", "created_at", "updated_at", "output_mapping", "output_keys") VALUES
	('22ac4021-e299-48e5-a73b-459893fab3b8', '4999dde9-2328-4636-8079-72363e4ea980', 'anime2', 'test', NULL, '{}', '{"cfg": 7, "seed": -1, "steps": 20, "width": 1024, "height": 1024, "denoise": 1, "ckpt_name": "checkpoint/wai-nsfw-illustrious-sdxl/illustrious/1761560-wainsfwillustrious_v140/1761560_waiNSFWIllustrious_v140.safetensors", "scheduler": "normal", "batch_size": 1, "sampler_name": "euler", "negative_prompt": "nsfw, blurry, low quality"}', '{"cfg": "3.inputs.cfg", "seed": "3.inputs.seed", "steps": "3.inputs.steps", "width": "71.inputs.width", "height": "71.inputs.height", "prompt": "74.inputs.text", "denoise": "3.inputs.denoise", "ckpt_name": "4.inputs.ckpt_name", "scheduler": "3.inputs.scheduler", "batch_size": "71.inputs.batch_size", "sampler_name": "3.inputs.sampler_name", "negative_prompt": "7.inputs.text"}', '{}', '{}', 'active', false, true, 0, 120, NULL, '2025-07-01 15:04:42.460481+00', '2025-07-01 15:04:59.878059+00', '{"images": "45"}', '{images}'),
	('e42f6430-04fa-47ca-bf45-e2c4cc6bd1f8', '9b839587-7baa-48a2-91da-14981c7dc984', 'dsgw', 'dsgw', NULL, '{}', '{"seed": -1, "lora_name": "【FLUX】古风玄幻推文   法术，魔法，御剑，修仙，御兽_v1.0.safetensors"}', '{"seed": "25.inputs.noise_seed", "prompt": "6.inputs.text", "lora_name": "58.inputs.lora_name"}', '{}', '{}', 'active', false, true, 0, 120, NULL, '2025-07-02 13:32:30.504772+00', '2025-07-02 13:34:28.880586+00', '{"images": "9"}', '{images}'),
	('cc85762e-1bd7-499b-b6ce-677d57391cd9', '3ba5bdd2-fc29-41d3-8eac-9ef5e31e08f6', 'kokoro-chinese-female', 'kokoro-chinese-female', NULL, '{}', '{"voice": "zf_001.pt"}', '{"voice": "40.inputs.voice", "prompt": "22.inputs.multi_line_prompt"}', '{}', '{}', 'active', false, true, 0, 120, NULL, '2025-07-04 09:21:28.723791+00', '2025-07-04 09:55:06.494227+00', '{"audio": "45"}', '{audio}');


--
-- Data for Name: novel2video_projects; Type: TABLE DATA; Schema: public; Owner: postgres
--

INSERT INTO "public"."novel2video_projects" ("id", "account_id", "title", "description", "original_text", "status", "settings", "progress", "created_at", "updated_at", "created_by", "updated_by") VALUES
	('42eb01fd-88c3-477e-996d-ea114622171d', 'c5b930c9-0a76-412e-a836-4bc4849a3270', '1111', '', '　　女子手中的剑指着淑月的咽喉，再进一分便可入肉。
　　符不离觉得，那剑好看的厉害，细薄柔韧，遍体银光，剑身上镶着蓝色的凤凰，精致得宛若下一刻就能飞起来。这样复杂的装饰会增加刺进去以及拔出来的难度，就实用性而言，这种东西用来刺人不如摆在墙上用来装饰。但握在这样的女子手里，却又无端的让人觉得，美好和肃杀是可以共存的。
　　剑光清冷地射在淑月的脸上，映得淑月的脸也显得略有苍白，显然也被吓了一跳。
　　淑月却又笑了起来：“哎呀，这大名鼎鼎的天才少女岚么？怎么突然有兴趣来我这里做客呢？”
　　“将人放了，要么，把头留下。”女子声音冰冷得有些沙哑。
　　她的脸被浅蓝色的面纱挡住，只有眼睛在外面，从身材能看出女子年纪应该不大，恐怕也就十几二十。可寻常女子怎会有那般的锐利眼神，其中的杀意与决绝，是只属于猎人的。
　　符不离有些呆了，一天之间能近距离接触到两位好看的女子，这简直能羡慕死之前的一帮兄弟了。
　　只可惜自己现在的打扮大约有点败风景，否则能和这二位站一起，倒也是一桩美事。
　　淑月两手抱胸：“你可要想清楚你在做什么，小丫头。”
　　方才还称呼对方为天才少女，这就变成小丫头了？
　　符不离暗暗诧异，这位看起来很柔弱的店主小姐姐，居然能做到如此临危不惧，普通的女孩子这时候已经吓得……花枝乱颤了吧？这个词是这么用的吧？
　　名叫岚的只是冷笑道：“世间的魔女有七位，少你一个也不要紧。你既然知道我的名字，就该知道我早就退休了，那些人定的规矩，对我早不适用了。”
　　“确实不缺我一个，但七魔女的名头又不是我起的，是你们定的。要我说，你们不如把我除名了比较好，我也一点都不喜欢和别人平起平坐，掉价的厉害。”
　　淑月一边说着，一边被岚以剑逼得一步步后退，逐渐退到了墙边。即便一直在后退，她脸上的笑容也没有因为压迫而露出半分胆怯。
　　“我说……你们要是有什么矛盾，要不先坐下来谈谈？”符不离说道。', 'draft', '{}', 0, '2025-07-01 15:07:48.093766+00', '2025-07-01 15:07:48.093766+00', 'c5b930c9-0a76-412e-a836-4bc4849a3270', 'c5b930c9-0a76-412e-a836-4bc4849a3270');