'use client';

import { useState } from 'react';

import { PlusIcon } from 'lucide-react';

import { Button } from '@kit/ui/button';
import { Trans } from '@kit/ui/trans';
import { cn } from '@kit/ui/utils';

import { CreateProjectDialog } from './create-project-dialog';
import { ProjectsList } from './projects-list';

export function Novel2VideoDashboard() {
  const [showCreateDialog, setShowCreateDialog] = useState(false);

  return (
    <div className="space-y-8">
      {/* Header with action - 移动端响应式 */}
      <div className="hero-elegant rounded-lg p-4 sm:p-6 mb-6 sm:mb-8">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div className="space-y-2 min-w-0">
            <h1 className="text-2xl sm:text-3xl font-bold tracking-tight text-ai-gradient-animated">
              <Trans i18nKey="novel2video:dashboard.yourProjects" />
            </h1>
            <p className="text-muted-foreground text-base sm:text-lg">
              <Trans i18nKey="novel2video:dashboard.projectsDescription" />
            </p>
          </div>

          <Button
            size="default"
            onClick={() => setShowCreateDialog(true)}
            className="btn-elegant ai-hover-lift w-full sm:w-auto min-h-[44px] sm:min-h-auto"
          >
            <PlusIcon className="mr-2 h-4 w-4" />
            <Trans i18nKey="novel2video:dashboard.createProject" />
          </Button>
        </div>
      </div>

      {/* Projects list */}
      <ProjectsList />

      {/* Create project dialog */}
      <CreateProjectDialog
        open={showCreateDialog}
        onOpenChange={setShowCreateDialog}
      />
    </div>
  );
}

