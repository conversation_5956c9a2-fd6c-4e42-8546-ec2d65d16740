'use client';

import { useState } from 'react';
import Image from 'next/image';
import { PhotoView } from 'react-photo-view';
import { ImageIcon } from 'lucide-react';
import { Loader2 } from 'lucide-react';
import { cn } from '@kit/ui/utils';
import 'react-photo-view/dist/react-photo-view.css';

interface ImageThumbnailProps {
  imageFile: {
    file_path: string;
    width?: number;
    height?: number;
    image_prompt?: string;
  } | null;
  className?: string;
  size?: 'sm' | 'md' | 'lg';
  enableLightbox?: boolean; // 新增：是否启用灯箱功能
}

export function ImageThumbnail({ imageFile, className = '', size = 'sm', enableLightbox = true }: ImageThumbnailProps) {
  const [imageError, setImageError] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  if (!imageFile || imageError) {
    const sizeClasses = {
      sm: 'h-7 w-7',
      md: 'h-9 w-9',
      lg: 'h-14 w-14'
    };

    return (
      <div className={cn(
        'flex items-center justify-center rounded-lg bg-purple-50 dark:bg-purple-900/30 border border-purple-200 dark:border-purple-800/50',
        sizeClasses[size],
        className
      )}>
        <ImageIcon className="h-3.5 w-3.5 text-purple-600 dark:text-purple-400" />
      </div>
    );
  }

  const sizeClasses = {
    sm: 'h-7 w-7',
    md: 'h-9 w-9',
    lg: 'h-14 w-14'
  };

  // 构建图片URL
  const imageUrl = `/api/image/${encodeURIComponent(imageFile.file_path)}`;

  const thumbnailContent = (
    <div
      className={cn(
        'group relative overflow-hidden rounded-lg border border-border/50 bg-muted/30',
        'hover:border-primary/30 hover:ring-1 hover:ring-primary/20 transition-all duration-200',
        enableLightbox ? 'cursor-pointer' : 'cursor-default',
        sizeClasses[size],
        className
      )}
    >
      {isLoading && (
        <div className="absolute inset-0 flex items-center justify-center bg-muted/30">
          <Loader2 className="h-3.5 w-3.5 animate-spin text-muted-foreground" />
        </div>
      )}
      <Image
        src={imageUrl}
        alt="Generated image"
        fill
        className={cn(
          'object-cover transition-all duration-300',
          enableLightbox && 'group-hover:scale-110',
          isLoading ? 'opacity-0' : 'opacity-100'
        )}
        onError={() => setImageError(true)}
        onLoadingComplete={() => setIsLoading(false)}
        sizes="(max-width: 768px) 32px, 48px"
      />
    </div>
  );

  return enableLightbox ? (
    <PhotoView src={imageUrl}>
      {thumbnailContent}
    </PhotoView>
  ) : (
    thumbnailContent
  );
}