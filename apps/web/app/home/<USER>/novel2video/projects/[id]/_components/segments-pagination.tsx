'use client';

import { ChevronLeft, ChevronRight } from 'lucide-react';

import { Button } from '@kit/ui/button';
import { Trans } from '@kit/ui/trans';

interface SegmentsPaginationProps {
  currentPage: number;
  totalItems: number;
  itemsPerPage: number;
  onPageChange: (page: number) => void;
}

export function SegmentsPagination({
  currentPage,
  totalItems,
  itemsPerPage,
  onPageChange,
}: SegmentsPaginationProps) {
  const totalPages = Math.ceil(totalItems / itemsPerPage);

  if (totalPages <= 1) {
    return null;
  }

  // 智能分页逻辑 - 确保当前页面始终可见，并显示合理的页面范围
  const getVisiblePages = () => {
    const maxVisible = 5; // 最多显示5个页码按钮

    // 如果总页数小于等于maxVisible，显示所有页面
    if (totalPages <= maxVisible) {
      return Array.from({ length: totalPages }, (_, i) => i + 1);
    }

    // 计算显示范围，确保当前页在中间位置
    let start = Math.max(1, currentPage - Math.floor(maxVisible / 2));
    const end = Math.min(totalPages, start + maxVisible - 1);

    // 如果end到了最后，调整start确保显示足够的页面
    if (end - start + 1 < maxVisible) {
      start = Math.max(1, end - maxVisible + 1);
    }

    const pages: (number | string)[] = [];

    // 添加第一页（如果不在当前范围内）
    if (start > 1) {
      pages.push(1);
      if (start > 2) {
        pages.push('...');
      }
    }

    // 添加当前范围内的页面
    for (let i = start; i <= end; i++) {
      pages.push(i);
    }

    // 添加最后一页（如果不在当前范围内）
    if (end < totalPages) {
      if (end < totalPages - 1) {
        pages.push('...');
      }
      pages.push(totalPages);
    }

    return pages;
  };

  const visiblePages = getVisiblePages();

  return (
    <div className="bg-gradient-to-r from-card/80 via-card/50 to-card/80 backdrop-blur-sm border-t border-border/50 p-3 sm:p-6 sticky bottom-0 z-10 pagination-fade-in">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3 max-w-screen-xl mx-auto">
        <div className="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-3">
          <div className="bg-primary/10 px-2 py-1 sm:px-3 sm:py-1.5 rounded-lg border border-primary/20">
            <span className="text-primary text-xs sm:text-sm font-semibold">
              <Trans
                i18nKey="novel2video:project.pagination.showing"
                values={{
                  start: (currentPage - 1) * itemsPerPage + 1,
                  end: Math.min(currentPage * itemsPerPage, totalItems),
                  total: totalItems,
                }}
                defaults="显示第 {{start}} - {{end}} 项，共 {{total}} 项"
              />
            </span>
          </div>
          <div className="text-muted-foreground text-xs">
            第 {currentPage} / {totalPages} 页
          </div>
        </div>

        <div className="flex items-center justify-center sm:justify-end gap-1 sm:gap-2 w-full sm:w-auto">
          <Button
            variant="outline"
            size="sm"
            onClick={() => onPageChange(Math.max(1, currentPage - 1))}
            disabled={currentPage === 1}
            className="h-10 sm:h-9 px-2 sm:px-3 bg-background/50 border-border/50 hover:bg-background/80 hover:shadow-sm transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed btn-glow min-h-[44px] sm:min-h-auto"
          >
            <ChevronLeft className="h-4 w-4 sm:mr-1" />
            <span className="hidden sm:inline">上一页</span>
          </Button>

          <div className="flex items-center gap-1 bg-muted/30 rounded-lg p-1 border border-border/30">
            {visiblePages.map((page, index) => {
              if (page === '...') {
                return (
                  <span
                    key={`ellipsis-${index}`}
                    className="text-muted-foreground px-2 sm:px-3 py-1.5 text-xs sm:text-sm"
                  >
                    ...
                  </span>
                );
              }

              const isCurrentPage = currentPage === page;
              return (
                <Button
                  key={page}
                  variant={isCurrentPage ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => onPageChange(page as number)}
                  className={`h-9 sm:h-8 min-w-9 sm:min-w-8 px-2 text-xs sm:text-sm font-medium transition-all duration-200 min-h-[44px] sm:min-h-auto ${isCurrentPage
                    ? 'bg-primary text-primary-foreground shadow-sm hover:bg-primary/90'
                    : 'hover:bg-muted/80 text-muted-foreground hover:text-foreground'
                    }`}
                >
                  {page}
                </Button>
              );
            })}
          </div>

          <Button
            variant="outline"
            size="sm"
            onClick={() =>
              onPageChange(Math.min(totalPages, currentPage + 1))
            }
            disabled={currentPage === totalPages}
            className="h-10 sm:h-9 px-2 sm:px-3 bg-background/50 border-border/50 hover:bg-background/80 hover:shadow-sm transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed btn-glow min-h-[44px] sm:min-h-auto"
          >
            <span className="hidden sm:inline">下一页</span>
            <ChevronRight className="h-4 w-4 sm:ml-1" />
          </Button>
        </div>
      </div>
    </div>
  );
}
