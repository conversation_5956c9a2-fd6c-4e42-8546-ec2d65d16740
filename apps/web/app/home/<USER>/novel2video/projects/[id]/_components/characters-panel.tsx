'use client';

import { useState, useEffect } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { motion, AnimatePresence, Variants } from 'framer-motion';
import { Button } from '@kit/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@kit/ui/card';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@kit/ui/dialog';
import { Badge } from '@kit/ui/badge';
import { Skeleton } from '@kit/ui/skeleton';
import { toast } from '@kit/ui/sonner';
import { Trash2, Edit, Plus, Users, UserIcon } from 'lucide-react';
import { Trans } from '@kit/ui/trans';
import { useSupabase } from '@kit/supabase/hooks/use-supabase';

import { getProjectCharactersAction, deleteCharacterAction } from '../../../_lib/server/server-actions';
import { CreateCharacterDialog } from './create-character-dialog';
import { EditCharacterDialog } from './edit-character-dialog';
import { ConfirmDialog } from './confirm-dialog';

// 动画变体
const listVariants: Variants = {
    hidden: { opacity: 0 },
    show: {
        opacity: 1,
        transition: {
            staggerChildren: 0.1
        }
    }
};

const cardVariants: Variants = {
    hidden: {
        opacity: 0,
        y: 20
    },
    show: {
        opacity: 1,
        y: 0,
        transition: {
            type: "spring" as const,
            stiffness: 260,
            damping: 20
        }
    }
};

type Character = {
    id: string;
    name: string;
    aliases: string[] | null;
    image_prompt: string | null;
    created_at: string;
    updated_at: string;
};

interface CharactersPanelProps {
    projectId: string;
}

export function CharactersPanel({ projectId }: CharactersPanelProps) {
    const [createDialogOpen, setCreateDialogOpen] = useState(false);
    const [editingCharacter, setEditingCharacter] = useState<Character | null>(null);
    const [confirmDialog, setConfirmDialog] = useState<{
        open: boolean;
        title: string;
        description: string;
        onConfirm: () => void;
    }>({
        open: false,
        title: '',
        description: '',
        onConfirm: () => { },
    });

    const queryClient = useQueryClient();
    const client = useSupabase();

    // 设置实时更新
    useEffect(() => {
        if (!projectId || !client) {
            console.log('🎭 Character realtime disabled:', { projectId, client: !!client });
            return;
        }

        console.log('🎭 Setting up character realtime for project:', projectId);

        const channel = client.channel(`novel2video-characters-${projectId}`);

        // 监听人物表的变化
        const subscription = channel
            .on(
                'postgres_changes',
                {
                    event: '*',
                    schema: 'public',
                    table: 'novel2video_characters',
                    filter: `project_id=eq.${projectId}`,
                },
                (payload) => {
                    console.log('🎭 Character update received:', {
                        eventType: payload.eventType,
                        new: payload.new,
                        old: payload.old
                    });

                    // 刷新人物列表
                    queryClient.invalidateQueries({ queryKey: ['project-characters', projectId] });

                    if (payload.eventType === 'INSERT') {
                        const newCharacter = payload.new as any;
                        toast.success(`新人物 "${newCharacter?.name}" 已添加！`);
                    } else if (payload.eventType === 'UPDATE') {
                        const updatedCharacter = payload.new as any;
                        toast.success(`人物 "${updatedCharacter?.name}" 信息已更新！`);
                    } else if (payload.eventType === 'DELETE') {
                        const deletedCharacter = payload.old as any;
                        toast.success(`人物 "${deletedCharacter?.name}" 已删除！`);
                    }
                }
            )
            .subscribe((status) => {
                console.log('🎭 Character channel subscription status:', status);
            });

        return () => {
            console.log('🎭 Unsubscribing from character channel:', projectId);
            subscription.unsubscribe();
        };
    }, [projectId, client, queryClient]);

    // 获取人物列表
    const { data: characters, isLoading } = useQuery({
        queryKey: ['project-characters', projectId],
        queryFn: async () => {
            const result = await getProjectCharactersAction({ projectId });
            if (!result.success) {
                throw new Error(result.error || '获取人物列表失败');
            }
            return result.characters;
        },
    });


    // 删除人物
    const deleteMutation = useMutation({
        mutationFn: async (characterId: string) => {
            const result = await deleteCharacterAction({ characterId });
            if (!result.success) {
                throw new Error(result.error || '删除失败');
            }
            return result;
        },
        onSuccess: (result) => {
            toast.success(result.message || '人物删除成功');
            queryClient.invalidateQueries({ queryKey: ['project-characters', projectId] });
        },
        onError: (error) => {
            toast.error(error.message);
        },
    });


    const handleDeleteCharacter = async (character: Character) => {
        setConfirmDialog({
            open: true,
            title: '删除人物',
            description: `确定要删除人物"${character.name}"吗？此操作无法撤销。`,
            onConfirm: async () => {
                await deleteMutation.mutateAsync(character.id);
                setConfirmDialog(prev => ({ ...prev, open: false }));
            },
        });
    };

    return (
        <div className="space-y-4">
            {/* 工具栏 - 移动端响应式 */}
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3">
                <div className="flex items-center gap-3 min-w-0">
                    <motion.div
                        className="bg-muted flex h-10 w-10 items-center justify-center rounded-lg flex-shrink-0"
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                    >
                        <Users className="h-5 w-5" />
                    </motion.div>
                    <div className="min-w-0">
                        <h2 className="text-lg font-semibold">
                            <Trans i18nKey="novel2video:charactersTitle" defaults="人物管理" />
                        </h2>
                        {characters && (
                            <p className="text-sm text-muted-foreground">
                                共 {characters.length} 个人物
                            </p>
                        )}
                    </div>
                </div>
                <div className="flex gap-2 w-full sm:w-auto">
                    <Dialog open={createDialogOpen} onOpenChange={setCreateDialogOpen}>
                        <DialogTrigger asChild>
                            <Button size="sm" className="gap-2 w-full sm:w-auto min-h-[44px] sm:min-h-auto">
                                <Plus className="h-4 w-4" />
                                添加人物
                            </Button>
                        </DialogTrigger>
                        <DialogContent>
                            <CreateCharacterDialog
                                projectId={projectId}
                                onSuccess={() => {
                                    setCreateDialogOpen(false);
                                    queryClient.invalidateQueries({ queryKey: ['project-characters', projectId] });
                                }}
                            />
                        </DialogContent>
                    </Dialog>
                </div>
            </div>

            {/* 人物列表 */}
            <div className="min-h-0 flex-1">
                {isLoading ? (
                    <motion.div
                        className="space-y-3"
                        variants={listVariants}
                        initial="hidden"
                        animate="show"
                    >
                        {[...Array(3)].map((_, i) => (
                            <motion.div
                                key={i}
                                variants={cardVariants}
                                className="flex items-center justify-between p-4 border rounded-lg"
                            >
                                <div className="space-y-2">
                                    <Skeleton className="h-4 w-24" />
                                    <Skeleton className="h-3 w-48" />
                                </div>
                                <div className="flex gap-2">
                                    <Skeleton className="h-8 w-8" />
                                    <Skeleton className="h-8 w-8" />
                                </div>
                            </motion.div>
                        ))}
                    </motion.div>
                ) : characters && characters.length > 0 ? (
                    <motion.div
                        className="space-y-3"
                        variants={listVariants}
                        initial="hidden"
                        animate="show"
                    >
                        <AnimatePresence mode="popLayout">
                            {characters.map((character) => (
                                <motion.div
                                    key={character.id}
                                    variants={cardVariants}
                                    initial="hidden"
                                    animate="show"
                                    exit={{ opacity: 0, y: -20 }}
                                    layout
                                >
                                    <Card>
                                        <CardContent className="p-4">
                                            <div className="flex flex-col sm:flex-row sm:items-start sm:justify-between gap-3">
                                                <div className="flex items-start gap-3 flex-1 min-w-0">
                                                    <motion.div
                                                        className="bg-muted flex h-8 w-8 items-center justify-center rounded-lg flex-shrink-0"
                                                        whileHover={{ scale: 1.05 }}
                                                        whileTap={{ scale: 0.95 }}
                                                    >
                                                        <UserIcon className="h-4 w-4" />
                                                    </motion.div>
                                                    <div className="flex-1 min-w-0">
                                                        <h4 className="font-medium text-sm truncate">{character.name}</h4>
                                                        {character.aliases && character.aliases.length > 0 && (
                                                            <div className="flex flex-wrap gap-1 mt-1">
                                                                {character.aliases.map((alias, index) => (
                                                                    <span
                                                                        key={index}
                                                                        className="inline-flex items-center px-1.5 py-0.5 rounded text-xs bg-blue-50 text-blue-600 border border-blue-200"
                                                                    >
                                                                        {alias}
                                                                    </span>
                                                                ))}
                                                            </div>
                                                        )}
                                                        {character.image_prompt && (
                                                            <p className="text-xs text-muted-foreground mt-1 line-clamp-2">
                                                                {character.image_prompt}
                                                            </p>
                                                        )}
                                                        <p className="text-xs text-muted-foreground mt-1">
                                                            创建于 {new Date(character.created_at).toLocaleDateString()}
                                                        </p>
                                                    </div>
                                                </div>
                                                <div className="flex gap-1 justify-end sm:justify-start sm:ml-2">
                                                    <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                                                        <Button
                                                            variant="ghost"
                                                            size="sm"
                                                            onClick={() => setEditingCharacter(character)}
                                                            className="h-8 w-8 p-0 min-h-[44px] sm:min-h-auto"
                                                        >
                                                            <Edit className="h-4 w-4" />
                                                        </Button>
                                                    </motion.div>
                                                    <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                                                        <Button
                                                            variant="ghost"
                                                            size="sm"
                                                            onClick={() => handleDeleteCharacter(character)}
                                                            disabled={deleteMutation.isPending}
                                                            className="h-8 w-8 p-0 text-destructive hover:text-destructive min-h-[44px] sm:min-h-auto"
                                                        >
                                                            <Trash2 className="h-4 w-4" />
                                                        </Button>
                                                    </motion.div>
                                                </div>
                                            </div>
                                        </CardContent>
                                    </Card>
                                </motion.div>
                            ))}
                        </AnimatePresence>
                    </motion.div>
                ) : (
                    <div className="flex flex-col items-center justify-center py-12 text-center">
                        <div className="rounded-full bg-muted p-3 mb-4">
                            <Users className="h-6 w-6 text-muted-foreground" />
                        </div>
                        <h3 className="text-lg font-medium mb-2">
                            <Trans i18nKey="novel2video:characters.empty.title" defaults="暂无人物信息" />
                        </h3>
                        <p className="text-muted-foreground text-sm max-w-sm">
                            <Trans i18nKey="novel2video:characters.empty.description" defaults="使用智能提取功能自动识别小说中的人物，或手动添加人物信息。" />
                        </p>
                    </div>
                )}
            </div>

            {editingCharacter && (
                <EditCharacterDialog
                    character={editingCharacter}
                    open={!!editingCharacter}
                    onOpenChange={(open: boolean) => !open && setEditingCharacter(null)}
                    onSuccess={() => {
                        setEditingCharacter(null);
                        queryClient.invalidateQueries({ queryKey: ['project-characters', projectId] });
                    }}
                />
            )}

            {/* 确认删除对话框 */}
            <ConfirmDialog
                open={confirmDialog.open}
                onOpenChange={(open) =>
                    setConfirmDialog((prev) => ({ ...prev, open }))
                }
                title={confirmDialog.title}
                description={confirmDialog.description}
                onConfirm={confirmDialog.onConfirm}
                variant="destructive"
            />
        </div>
    );
} 