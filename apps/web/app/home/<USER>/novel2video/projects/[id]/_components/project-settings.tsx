'use client';

import { useEffect, useState } from 'react';

import { zodResolver } from '@hookform/resolvers/zod';
import {
  CheckCircle,
  Image,
  Save,
  Settings,
  Video,
  Volume2,
} from 'lucide-react';
import { useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';

import { Badge } from '@kit/ui/badge';
import { Button } from '@kit/ui/button';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@kit/ui/dialog';
import { Form } from '@kit/ui/form';
import { useIsMobile } from '@kit/ui/use-mobile';
import { Progress } from '@kit/ui/progress';
import { toast } from '@kit/ui/sonner';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@kit/ui/tabs';

import {
  type ProjectSettingsFormData,
  ProjectSettingsSchema,
} from '../../../_lib/schema/project-settings.schema';
import {
  updateProjectSettingsAction,
} from '../../../_lib/server/project-settings-actions';
import { AudioSettings } from './settings/audio-settings';
import { ImageSettings } from './settings/image-settings';
import { VideoSettings } from './settings/video-settings';

interface ProjectSettingsProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  projectId: string;
  currentSettings: ProjectSettingsFormData;
  onSettingsUpdate: (settings: ProjectSettingsFormData) => void;
}

const SETTINGS_TABS = [
  {
    id: 'audio',
    icon: Volume2,
    title: 'audio.title',
    description: 'audio.description',
    color: 'text-blue-600 bg-blue-50 dark:text-blue-400 dark:bg-blue-950',
  },
  {
    id: 'image',
    icon: Image,
    title: 'image.title',
    description: 'image.description',
    color:
      'text-purple-600 bg-purple-50 dark:text-purple-400 dark:bg-purple-950',
  },
  {
    id: 'video',
    icon: Video,
    title: 'video.title',
    description: 'video.description',
    color: 'text-green-600 bg-green-50 dark:text-green-400 dark:bg-green-950',
  },
] as const;

type TabId = (typeof SETTINGS_TABS)[number]['id'];

export function ProjectSettings({
  open,
  onOpenChange,
  projectId,
  currentSettings,
  onSettingsUpdate,
}: ProjectSettingsProps) {
  const { t } = useTranslation('novel2video');
  const [activeTab, setActiveTab] = useState<TabId>('audio');
  const [isSaving, setIsSaving] = useState(false);
  const isMobile = useIsMobile();

  const form = useForm<ProjectSettings>({
    resolver: zodResolver(ProjectSettingsSchema),
    defaultValues: currentSettings,
  });

  const { watch, formState } = form;
  const watchedValues = watch();

  // 计算每个标签的配置完成度
  const getTabProgress = (tabId: TabId): number => {
    const tabData = watchedValues[tabId];
    if (!tabData) return 0;

    const fields = Object.values(tabData);
    const filledFields = fields.filter(
      (value) =>
        value !== undefined &&
        value !== null &&
        value !== '' &&
        value !== false,
    );

    return Math.round((filledFields.length / fields.length) * 100);
  };

  // 当设置变化时重置表单
  useEffect(() => {
    if (open) {
      form.reset(currentSettings);
    }
  }, [open, currentSettings, form]);

  // 保存设置
  const handleSave = async () => {
    try {
      setIsSaving(true);
      const values = form.getValues();
      const result = await updateProjectSettingsAction({
        projectId,
        settings: values,
      });

      if (result.success) {
        // 立即更新父组件的设置状态
        onSettingsUpdate(values);
        toast.success(t('projectSettings.saveSuccess'));
        onOpenChange(false);
      } else {
        toast.error(
          t('projectSettings.saveError') +
          (result.error ? ': ' + result.error : ''),
        );
      }
    } catch (error) {
      console.error('Failed to save settings:', error);
      toast.error(t('projectSettings.saveError'));
    } finally {
      setIsSaving(false);
    }
  };

  const activeTabData = SETTINGS_TABS.find((tab) => tab.id === activeTab);

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent
        className={`overflow-hidden p-0 ${isMobile
          ? 'h-[95vh] max-w-[95vw] w-[95vw]'
          : 'h-[90vh] max-w-7xl'
          }`}
      >
        <DialogHeader className="from-background to-accent/20 border-b bg-gradient-to-r px-4 py-3 md:px-6 md:py-4">
          <DialogTitle className="text-gradient-elegant flex items-center gap-3 text-lg md:text-xl">
            <div className="bg-primary/10 rounded-lg p-2">
              <Settings className="text-primary h-4 w-4 md:h-5 md:w-5" />
            </div>
            {t('projectSettings.title')}
          </DialogTitle>
        </DialogHeader>

        <Form {...form}>
          {isMobile ? (
            // 移动端布局：使用 Tabs 组件
            <div className="flex h-[calc(95vh-4rem)] flex-col overflow-hidden">
              <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as TabId)} className="flex h-full flex-col">
                {/* 移动端标签栏 */}
                <div className="border-b px-4 py-2">
                  <TabsList className="grid w-full grid-cols-3">
                    {SETTINGS_TABS.map((tab) => {
                      const Icon = tab.icon;
                      const progress = getTabProgress(tab.id);
                      const isCompleted = progress > 80;

                      return (
                        <TabsTrigger
                          key={tab.id}
                          value={tab.id}
                          className="flex flex-col items-center gap-1 py-3 text-xs"
                        >
                          <div className="flex items-center gap-1">
                            <Icon className="h-4 w-4" />
                            {isCompleted && (
                              <CheckCircle className="h-3 w-3 text-green-500" />
                            )}
                          </div>
                          <span className="hidden sm:inline">
                            {t(`projectSettings.${tab.title}`)}
                          </span>
                          <div className="text-xs text-muted-foreground">
                            {progress}%
                          </div>
                        </TabsTrigger>
                      );
                    })}
                  </TabsList>
                </div>

                {/* 移动端内容区域 */}
                <div className="flex-1 overflow-hidden">
                  {SETTINGS_TABS.map((tab) => (
                    <TabsContent
                      key={tab.id}
                      value={tab.id}
                      className="h-full overflow-hidden p-0 m-0"
                    >
                      {/* 移动端内容头部 */}
                      <div className="border-b px-4 py-3">
                        <div className="flex items-center gap-3">
                          <div className={`rounded-lg p-2 ${tab.color}`}>
                            <tab.icon className="h-4 w-4" />
                          </div>
                          <div className="flex-1">
                            <h2 className="text-base font-semibold">
                              {t(`projectSettings.${tab.title}`)}
                            </h2>
                            <p className="text-muted-foreground text-xs">
                              {t(`projectSettings.${tab.description}`)}
                            </p>
                          </div>
                          <Badge
                            variant="secondary"
                            className="bg-primary/10 text-primary border-primary/20 border px-2 py-1 text-xs"
                          >
                            {getTabProgress(tab.id)}%
                          </Badge>
                        </div>
                      </div>

                      {/* 移动端设置内容 */}
                      <div className="custom-scrollbar flex-1 overflow-y-auto p-4">
                        {tab.id === 'audio' && <AudioSettings form={form} />}
                        {tab.id === 'image' && <ImageSettings form={form} />}
                        {tab.id === 'video' && <VideoSettings form={form} />}
                      </div>
                    </TabsContent>
                  ))}
                </div>

                {/* 移动端保存按钮 */}
                <div className="border-t p-4">
                  <Button
                    onClick={handleSave}
                    disabled={isSaving || !formState.isValid}
                    className="flex h-12 w-full items-center gap-3 text-sm font-medium"
                    size="default"
                  >
                    {isSaving ? (
                      <div className="h-4 w-4 animate-spin rounded-full border-2 border-white border-t-transparent"></div>
                    ) : (
                      <Save className="h-4 w-4" />
                    )}
                    {t('projectSettings.save')}
                  </Button>
                </div>
              </Tabs>
            </div>
          ) : (
            // 桌面端布局：保持原有的左右分栏布局
            <div className="flex h-[calc(90vh-5rem)] overflow-hidden">
              {/* 左侧导航栏 */}
              <div className="bg-accent/5 custom-scrollbar w-80 overflow-y-auto border-r p-4">
                <div className="space-y-3">
                  {SETTINGS_TABS.map((tab) => {
                    const Icon = tab.icon;
                    const isActive = tab.id === activeTab;
                    const progress = getTabProgress(tab.id);
                    const isCompleted = progress > 80;

                    return (
                      <button
                        key={tab.id}
                        onClick={() => setActiveTab(tab.id)}
                        className={`group w-full rounded-xl border-2 p-4 text-left transition-all duration-300 ${isActive
                          ? 'border-primary bg-primary/5 ring-primary/20 scale-[1.02] shadow-lg ring-1'
                          : 'border-border hover:border-primary/40 hover:bg-accent/50 hover:shadow-md'
                          }`}
                      >
                        <div className="mb-3 flex items-start justify-between">
                          <div
                            className={`rounded-xl p-2.5 transition-all duration-300 ${isActive
                              ? tab.color
                              : 'text-muted-foreground bg-accent group-hover:bg-primary/10 group-hover:text-primary'
                              }`}
                          >
                            <Icon className="h-5 w-5" />
                          </div>
                          {isCompleted && (
                            <CheckCircle className="h-5 w-5 text-green-500" />
                          )}
                        </div>

                        <div className="space-y-2">
                          <h3
                            className={`text-sm font-semibold ${isActive
                              ? 'text-primary'
                              : 'text-foreground group-hover:text-primary'
                              }`}
                          >
                            {t(`projectSettings.${tab.title}`)}
                          </h3>

                          <p className="text-muted-foreground line-clamp-2 text-xs leading-relaxed">
                            {t(`projectSettings.${tab.description}`)}
                          </p>

                          <div className="flex items-center gap-2 pt-1">
                            <Progress
                              value={progress}
                              className="bg-secondary h-2 flex-1"
                            />
                            <span className="text-muted-foreground min-w-[3rem] text-right text-xs font-medium">
                              {progress}%
                            </span>
                          </div>
                        </div>
                      </button>
                    );
                  })}
                </div>

                {/* 保存按钮 */}
                <div className="mt-4 border-t pt-4">
                  <Button
                    onClick={handleSave}
                    disabled={isSaving || !formState.isValid}
                    className="flex h-12 w-full items-center gap-3 text-sm font-medium"
                    size="default"
                  >
                    {isSaving ? (
                      <div className="h-4 w-4 animate-spin rounded-full border-2 border-white border-t-transparent"></div>
                    ) : (
                      <Save className="h-4 w-4" />
                    )}
                    {t('projectSettings.save')}
                  </Button>
                </div>
              </div>

              {/* 右侧内容区域 */}
              <div className="flex flex-1 flex-col overflow-hidden">
                {/* 内容头部 */}
                <div className="from-background to-accent/10 border-b bg-gradient-to-r px-6 py-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      {activeTabData && (
                        <div
                          className={`rounded-lg p-2 ${activeTabData.color}`}
                        >
                          <activeTabData.icon className="h-5 w-5" />
                        </div>
                      )}
                      <div>
                        <h2 className="text-lg font-semibold">
                          {activeTabData &&
                            t(`projectSettings.${activeTabData.title}`)}
                        </h2>
                        <p className="text-muted-foreground mt-1 text-sm">
                          {activeTabData &&
                            t(`projectSettings.${activeTabData.description}`)}
                        </p>
                      </div>
                    </div>

                    <Badge
                      variant="secondary"
                      className="bg-primary/10 text-primary border-primary/20 border px-3 py-1 text-xs font-medium"
                    >
                      {getTabProgress(activeTab)}%{' '}
                      {t('projectSettings.completed')}
                    </Badge>
                  </div>
                </div>

                {/* 设置内容 */}
                <div className="custom-scrollbar flex-1 overflow-y-auto p-6">
                  {activeTab === 'audio' && <AudioSettings form={form} />}
                  {activeTab === 'image' && <ImageSettings form={form} />}
                  {activeTab === 'video' && <VideoSettings form={form} />}
                </div>
              </div>
            </div>
          )}
        </Form>
      </DialogContent>
    </Dialog>
  );
}
