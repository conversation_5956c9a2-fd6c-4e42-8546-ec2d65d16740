'use client';

import { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence, useAnimation } from 'framer-motion';
import {
  ImageIcon,
  SpeakerIcon,
  Play,
  Pause,
  Users,
  Search,
  Sparkles,
  FileText,
  Video,
  Zap,
  ChevronDownIcon,
  ChevronUpIcon,
  Loader2
} from 'lucide-react';

import { Button } from '@kit/ui/button';
import { Progress } from '@kit/ui/progress';
import { Trans } from '@kit/ui/trans';
import { Badge } from '@kit/ui/badge';
import { Card } from '@kit/ui/card';
import { cn } from '@kit/ui/utils';
import { toast } from '@kit/ui/sonner';

// 动画数字组件
function AnimatedNumber({ value, duration = 500 }: { value: number; duration?: number }) {
  const [displayValue, setDisplayValue] = useState(value);
  const [isAnimating, setIsAnimating] = useState(false);

  useEffect(() => {
    if (displayValue === value) return;

    setIsAnimating(true);
    const startValue = displayValue;
    const startTime = performance.now();

    const animate = (currentTime: number) => {
      const elapsed = currentTime - startTime;
      const progress = Math.min(elapsed / duration, 1);

      // easeOutCubic缓动
      const easeProgress = 1 - Math.pow(1 - progress, 3);
      const current = startValue + (value - startValue) * easeProgress;

      setDisplayValue(Math.round(current));

      if (progress < 1) {
        requestAnimationFrame(animate);
      } else {
        setIsAnimating(false);
      }
    };

    requestAnimationFrame(animate);
  }, [value, duration, displayValue]);

  return (
    <motion.span
      className="inline-block"
      animate={isAnimating ? {
        scale: [1, 1.1, 1],
        color: ['rgb(var(--foreground))', 'rgb(var(--primary))', 'rgb(var(--foreground))']
      } : {}}
      transition={{ duration: 0.3 }}
    >
      {displayValue}
    </motion.span>
  );
}

// 美观的进度步骤组件
function ElegantProgressStep({
  icon,
  label,
  current,
  total,
  percentage,
  status,
  isLoading,
  onAction,
  actionLabel,
  color = 'blue'
}: {
  icon: React.ReactNode;
  label: string;
  current: number;
  total: number;
  percentage: number;
  status: 'pending' | 'processing' | 'completed' | 'error';
  isLoading?: boolean;
  onAction?: () => void;
  actionLabel?: string;
  color?: 'blue' | 'green' | 'purple' | 'orange' | 'cyan' | 'indigo';
}) {
  const controls = useAnimation();
  const [hasRecentUpdate, setHasRecentUpdate] = useState(false);
  const prevCurrent = useRef(current);

  // 检测数值变化并触发动画
  useEffect(() => {
    if (prevCurrent.current !== current && prevCurrent.current !== undefined) {
      setHasRecentUpdate(true);
      controls.start({
        scale: [1, 1.02, 1],
        boxShadow: [
          '0 0 0 0 rgba(59, 130, 246, 0)',
          '0 0 0 4px rgba(59, 130, 246, 0.15)',
          '0 0 0 0 rgba(59, 130, 246, 0)'
        ]
      });

      setTimeout(() => setHasRecentUpdate(false), 2000);
    }
    prevCurrent.current = current;
  }, [current, controls]);

  const getColorClasses = () => {
    switch (color) {
      case 'green':
        return {
          icon: 'text-green-600 dark:text-green-400',
          bg: 'bg-green-500/10 border-green-500/20',
          progress: 'bg-green-500',
          button: 'border-green-200 dark:border-green-800 hover:bg-green-50 dark:hover:bg-green-950/50'
        };
      case 'purple':
        return {
          icon: 'text-purple-600 dark:text-purple-400',
          bg: 'bg-purple-500/10 border-purple-500/20',
          progress: 'bg-purple-500',
          button: 'border-purple-200 dark:border-purple-800 hover:bg-purple-50 dark:hover:bg-purple-950/50'
        };
      case 'orange':
        return {
          icon: 'text-orange-600 dark:text-orange-400',
          bg: 'bg-orange-500/10 border-orange-500/20',
          progress: 'bg-orange-500',
          button: 'border-orange-200 dark:border-orange-800 hover:bg-orange-50 dark:hover:bg-orange-950/50'
        };
      case 'cyan':
        return {
          icon: 'text-cyan-600 dark:text-cyan-400',
          bg: 'bg-cyan-500/10 border-cyan-500/20',
          progress: 'bg-cyan-500',
          button: 'border-cyan-200 dark:border-cyan-800 hover:bg-cyan-50 dark:hover:bg-cyan-950/50'
        };
      case 'indigo':
        return {
          icon: 'text-indigo-600 dark:text-indigo-400',
          bg: 'bg-indigo-500/10 border-indigo-500/20',
          progress: 'bg-indigo-500',
          button: 'border-indigo-200 dark:border-indigo-800 hover:bg-indigo-50 dark:hover:bg-indigo-950/50'
        };
      default: // blue
        return {
          icon: 'text-blue-600 dark:text-blue-400',
          bg: 'bg-blue-500/10 border-blue-500/20',
          progress: 'bg-blue-500',
          button: 'border-blue-200 dark:border-blue-800 hover:bg-blue-50 dark:hover:bg-blue-950/50'
        };
    }
  };

  const colors = getColorClasses();

  return (
    <motion.div
      animate={controls}
      className={cn(
        'relative flex flex-col sm:flex-row sm:items-center gap-3 p-3 rounded-lg border bg-card/50 backdrop-blur-sm',
        'hover:shadow-sm transition-all duration-200',
        hasRecentUpdate && 'ring-1 ring-primary/20'
      )}
    >
      <div className="flex items-center gap-3 flex-1 min-w-0">
        {/* 图标 */}
        <motion.div
          className={cn('flex h-8 w-8 items-center justify-center rounded-lg border flex-shrink-0', colors.bg)}
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
        >
          {isLoading ? (
            <Loader2 className={cn('h-4 w-4 animate-spin', colors.icon)} />
          ) : (
            <div className={colors.icon}>{icon}</div>
          )}
        </motion.div>

        {/* 标签和进度 */}
        <div className="flex-1 min-w-0">
          <div className="flex items-center justify-between mb-1">
            <span className="text-sm font-medium text-foreground truncate">{label}</span>
            <div className="flex items-center gap-2 flex-shrink-0">
              <span className="text-xs font-semibold text-foreground">
                <AnimatedNumber value={current} />
                <span className="text-muted-foreground">/{total}</span>
              </span>
              {hasRecentUpdate && (
                <motion.div
                  initial={{ opacity: 0, scale: 0 }}
                  animate={{ opacity: 1, scale: 1 }}
                  exit={{ opacity: 0, scale: 0 }}
                  className="text-green-500"
                >
                  <Zap className="h-3 w-3" />
                </motion.div>
              )}
            </div>
          </div>

          {/* 进度条 */}
          <div className="relative overflow-hidden rounded-full">
            <Progress
              value={percentage}
              className="h-2"
            />
            {status === 'processing' && (
              <motion.div
                className="absolute inset-0 h-2 bg-gradient-to-r from-transparent via-white/30 to-transparent"
                animate={{ x: ['-100%', '100%'] }}
                transition={{ duration: 2, repeat: Infinity, ease: 'linear' }}
              />
            )}
          </div>
        </div>
      </div>

      {/* 操作按钮 - 移动端全宽 */}
      {onAction && actionLabel && (
        <motion.div whileHover={{ scale: 1.02 }} whileTap={{ scale: 0.98 }} className="w-full sm:w-auto">
          <Button
            variant="outline"
            size="sm"
            onClick={onAction}
            disabled={isLoading}
            className={cn('h-8 px-3 text-xs w-full sm:w-auto min-h-[44px] sm:min-h-auto', colors.button)}
          >
            {isLoading ? (
              <Loader2 className="h-3 w-3 animate-spin" />
            ) : (
              <Sparkles className="h-3 w-3 mr-1" />
            )}
            {actionLabel}
          </Button>
        </motion.div>
      )}
    </motion.div>
  );
}

interface CompactProjectProgressProps {
  project: {
    progress: number;
    status: string;
    [key: string]: any;
  };
  segments: Array<{
    status: string;
    audioFile?: any;
    imageFile?: any;
    image_prompt?: string | null;
    characters?: Array<{
      id: string;
      name: string;
      image_prompt?: string | null;
    }>;
    [key: string]: any;
  }>;
  characters?: Array<{
    id: string;
    name: string;
    aliases: string[] | null;
    image_prompt: string | null;
    [key: string]: any;
  }>;
  onStartProcessing?: () => void;
  onPauseProcessing?: () => void;
  onGeneratePrompts?: () => void;
  onRegenerateAllPrompts?: () => void;
  onGenerateImages?: (configId?: string) => void;
  onRegenerateAllImages?: (configId?: string) => void;
  onGenerateAudio?: () => void;
  onRegenerateAllAudio?: () => void;
  onExtractCharacters?: () => void;
  onAnalyzeSegmentCharacters?: () => void;
  onReanalyzeAllSegmentCharacters?: () => void;
  isProcessing?: boolean;
  isGeneratingPrompts?: boolean;
  isGeneratingImages?: boolean;
  isGeneratingAudio?: boolean;
  isExtractingCharacters?: boolean;
  isAnalyzingSegments?: boolean;
  onMergeVideo?: () => void;
}

export function ProjectProgress({
  project,
  segments,
  characters,
  onStartProcessing,
  onPauseProcessing,
  onGeneratePrompts,
  onRegenerateAllPrompts,
  onGenerateImages,
  onRegenerateAllImages,
  onGenerateAudio,
  onRegenerateAllAudio,
  onExtractCharacters,
  onAnalyzeSegmentCharacters,
  onReanalyzeAllSegmentCharacters,
  isProcessing,
  isGeneratingPrompts,
  isGeneratingImages,
  isGeneratingAudio,
  isExtractingCharacters,
  isAnalyzingSegments,
  onMergeVideo
}: CompactProjectProgressProps) {
  const [isExpanded, setIsExpanded] = useState(false);
  const totalSegments = segments.length;
  const charactersCount = characters?.length || 0;

  // 计算各步骤进度
  const segmentsWithCharacters = segments.filter(s => s.characters && s.characters.length > 0).length;
  const promptGeneratedSegments = segments.filter(s => s.image_prompt).length;
  const imageGeneratedSegments = segments.filter(s => s.imageFile).length;
  const audioGeneratedSegments = segments.filter(s => s.audioFile).length;
  const completedSegments = segments.filter(s => s.status === 'completed').length;
  const overallProgress = project.progress || 0;

  const getStatus = (current: number, total: number, isLoading?: boolean) => {
    if (isLoading) return 'processing';
    if (current === total && total > 0) return 'completed';
    if (current > 0) return 'processing';
    return 'pending';
  };

  return (
    <motion.div
      className="border-b bg-gradient-to-r from-background via-muted/10 to-background"
      initial={{ height: 'auto' }}
      animate={{ height: 'auto' }}
      transition={{ duration: 0.3 }}
    >
      {/* 总体进度和控制栏 - 移动端响应式 */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between px-4 py-3 gap-3">
        <div className="flex flex-col sm:flex-row sm:items-center gap-3 sm:gap-4 flex-1 min-w-0">
          {/* 总体进度 */}
          <div className="flex items-center gap-3 min-w-0">
            <span className="text-sm font-semibold flex-shrink-0">
              <Trans i18nKey="novel2video:project.progress.title" defaults="Processing Progress" />
            </span>
            <div className="relative overflow-hidden rounded-full flex-1 max-w-32">
              <Progress value={overallProgress} className="w-full h-3" />
              {isProcessing && (
                <motion.div
                  className="absolute inset-0 h-3 bg-gradient-to-r from-transparent via-primary/30 to-transparent"
                  animate={{ x: ['-100%', '100%'] }}
                  transition={{ duration: 2, repeat: Infinity, ease: 'linear' }}
                />
              )}
            </div>
            <motion.span
              className="text-sm font-bold text-primary min-w-[3ch] flex-shrink-0"
              key={overallProgress}
              initial={{ scale: 1 }}
              animate={{ scale: [1, 1.1, 1] }}
              transition={{ duration: 0.3 }}
            >
              {Math.round(overallProgress)}%
            </motion.span>
          </div>

          {/* 快速状态概览 - 移动端换行 */}
          <div className="flex items-center gap-2 sm:gap-3 text-xs text-muted-foreground flex-wrap">
            <span className="flex items-center gap-1">
              <Users className="h-3 w-3" />
              <AnimatedNumber value={charactersCount} />
            </span>
            <span className="flex items-center gap-1">
              <FileText className="h-3 w-3" />
              <AnimatedNumber value={promptGeneratedSegments} />/{totalSegments}
            </span>
            <span className="flex items-center gap-1">
              <ImageIcon className="h-3 w-3" />
              <AnimatedNumber value={imageGeneratedSegments} />/{totalSegments}
            </span>
            <span className="flex items-center gap-1">
              <SpeakerIcon className="h-3 w-3" />
              <AnimatedNumber value={audioGeneratedSegments} />/{totalSegments}
            </span>
          </div>
        </div>

        {/* 操作按钮组 - 移动端响应式 */}
        <div className="flex items-center justify-between sm:justify-end gap-2 flex-wrap w-full sm:w-auto">
          <Badge variant={project.status === 'completed' ? 'default' : 'secondary'} className="text-xs">
            <Trans i18nKey={`novel2video:status.project.${project.status}`} defaults={project.status} />
          </Badge>

          <div className="flex items-center gap-2">
            <motion.div whileHover={{ scale: 1.02 }} whileTap={{ scale: 0.98 }}>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsExpanded(!isExpanded)}
                className="h-8 px-2 text-xs min-h-[44px] sm:min-h-auto"
              >
                <span className="hidden sm:inline">详情</span>
                <motion.div
                  animate={{ rotate: isExpanded ? 180 : 0 }}
                  transition={{ duration: 0.2 }}
                >
                  <ChevronDownIcon className="h-3 w-3 sm:ml-1" />
                </motion.div>
              </Button>
            </motion.div>

            {project.status === 'draft' || project.status === 'failed' ? (
              <motion.div whileHover={{ scale: 1.02 }} whileTap={{ scale: 0.98 }}>
                <Button size="sm" onClick={onStartProcessing} disabled={!onStartProcessing} className="min-h-[44px] sm:min-h-auto">
                  <Play className="h-3 w-3 mr-1" />
                  <span className="text-xs sm:text-sm">开始处理</span>
                </Button>
              </motion.div>
            ) : project.status === 'processing' ? (
              <motion.div whileHover={{ scale: 1.02 }} whileTap={{ scale: 0.98 }}>
                <Button variant="outline" size="sm" onClick={onPauseProcessing} disabled={!onPauseProcessing} className="min-h-[44px] sm:min-h-auto">
                  <Pause className="h-3 w-3 mr-1" />
                  <span className="text-xs sm:text-sm">暂停</span>
                </Button>
              </motion.div>
            ) : null}
          </div>
        </div>
      </div>

      {/* 详细步骤进度 */}
      <AnimatePresence>
        {isExpanded && (
          <motion.div
            initial={{ height: 0, opacity: 0 }}
            animate={{ height: 'auto', opacity: 1 }}
            exit={{ height: 0, opacity: 0 }}
            transition={{ duration: 0.3, ease: 'easeInOut' }}
            className="overflow-hidden"
          >
            <div className="px-4 pb-4 space-y-3">
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3">
                {/* 人物提取 */}
                <ElegantProgressStep
                  icon={<Users className="h-4 w-4" />}
                  label="人物提取"
                  current={charactersCount}
                  total={Math.max(charactersCount, 1)}
                  percentage={charactersCount > 0 ? 100 : 0}
                  status={getStatus(charactersCount, 1, isExtractingCharacters)}
                  isLoading={isExtractingCharacters}
                  onAction={onExtractCharacters}
                  actionLabel={charactersCount > 0 ? "重提取" : "提取"}
                  color="indigo"
                />

                {/* 段落分析 */}
                <ElegantProgressStep
                  icon={<Search className="h-4 w-4" />}
                  label="段落分析"
                  current={segmentsWithCharacters}
                  total={totalSegments}
                  percentage={totalSegments > 0 ? (segmentsWithCharacters / totalSegments) * 100 : 0}
                  status={getStatus(segmentsWithCharacters, totalSegments, isAnalyzingSegments)}
                  isLoading={isAnalyzingSegments}
                  onAction={segmentsWithCharacters > 0 ? onReanalyzeAllSegmentCharacters : onAnalyzeSegmentCharacters}
                  actionLabel={segmentsWithCharacters > 0 ? "重分析" : "分析"}
                  color="cyan"
                />

                {/* 生成提示词 */}
                <ElegantProgressStep
                  icon={<FileText className="h-4 w-4" />}
                  label="生成提示词"
                  current={promptGeneratedSegments}
                  total={totalSegments}
                  percentage={totalSegments > 0 ? (promptGeneratedSegments / totalSegments) * 100 : 0}
                  status={getStatus(promptGeneratedSegments, totalSegments, isGeneratingPrompts)}
                  isLoading={isGeneratingPrompts}
                  onAction={promptGeneratedSegments > 0 ? onRegenerateAllPrompts : onGeneratePrompts}
                  actionLabel={promptGeneratedSegments > 0 ? "重生成" : "生成"}
                  color="orange"
                />

                {/* 生成图片 */}
                <ElegantProgressStep
                  icon={<ImageIcon className="h-4 w-4" />}
                  label="生成图片"
                  current={imageGeneratedSegments}
                  total={totalSegments}
                  percentage={totalSegments > 0 ? (imageGeneratedSegments / totalSegments) * 100 : 0}
                  status={getStatus(imageGeneratedSegments, totalSegments, isGeneratingImages)}
                  isLoading={isGeneratingImages}
                  onAction={imageGeneratedSegments > 0 ? onRegenerateAllImages : onGenerateImages}
                  actionLabel={imageGeneratedSegments > 0 ? "重生成" : "生成"}
                  color="green"
                />

                {/* 生成音频 */}
                <ElegantProgressStep
                  icon={<SpeakerIcon className="h-4 w-4" />}
                  label="生成音频"
                  current={audioGeneratedSegments}
                  total={totalSegments}
                  percentage={totalSegments > 0 ? (audioGeneratedSegments / totalSegments) * 100 : 0}
                  status={getStatus(audioGeneratedSegments, totalSegments, isGeneratingAudio)}
                  isLoading={isGeneratingAudio}
                  onAction={audioGeneratedSegments > 0 ? onRegenerateAllAudio : onGenerateAudio}
                  actionLabel={audioGeneratedSegments > 0 ? "重生成" : "生成"}
                  color="purple"
                />

                {/* 合成视频 */}
                <ElegantProgressStep
                  icon={<Video className="h-4 w-4" />}
                  label="合成视频"
                  current={project.status === 'completed' ? 1 : 0}
                  total={1}
                  percentage={project.status === 'completed' ? 100 : 0}
                  status={project.status === 'completed' ? 'completed' : 'pending'}
                  isLoading={false}
                  onAction={() => {
                    // 检查是否所有段落都有音频和图片
                    const segmentsWithBoth = segments.filter(s => s.audioFile && s.imageFile).length;
                    if (segmentsWithBoth < segments.length) {
                      toast.error(`还有 ${segments.length - segmentsWithBoth} 个段落缺少音频或图片，请先完成所有媒体生成`);
                      return;
                    }

                    // 启动视频合成（无论是首次还是重新合成）
                    onMergeVideo?.();
                  }}
                  actionLabel={project.status === 'completed' ? "重新合成" : "合成"}
                  color="blue"
                />
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </motion.div>
  );
} 