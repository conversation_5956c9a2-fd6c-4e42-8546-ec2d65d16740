'use client';

import { Filter, Search } from 'lucide-react';
import { motion } from 'framer-motion';
import { useTranslation } from 'react-i18next';

import { Button } from '@kit/ui/button';
import { Input } from '@kit/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@kit/ui/select';
import { Trans } from '@kit/ui/trans';

interface SegmentsToolbarProps {
  searchTerm: string;
  statusFilter: string;
  onSearchChange: (value: string) => void;
  onStatusFilterChange: (value: string) => void;
}

export function SegmentsToolbar({
  searchTerm,
  statusFilter,
  onSearchChange,
  onStatusFilterChange,
}: SegmentsToolbarProps) {
  const { t } = useTranslation();

  return (
    <motion.div
      className="flex flex-col space-y-3"
      initial={{ opacity: 0, y: -20 }}
      animate={{ opacity: 1, y: 0 }}
    >
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3">
        <motion.div
          className="relative flex-1 sm:max-w-sm"
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.1 }}
        >
          <Search className="text-muted-foreground absolute left-3 top-3 h-4 w-4" />
          <Input
            placeholder="Search text segments..."
            value={searchTerm}
            onChange={(e) => onSearchChange(e.target.value)}
            className="pl-10 h-12 sm:h-10"
          />
        </motion.div>

        <motion.div
          className="flex items-center space-x-2"
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.2 }}
        >
          <Filter className="text-muted-foreground h-4 w-4 hidden sm:block" />
          <Select value={statusFilter} onValueChange={onStatusFilterChange}>
            <SelectTrigger className="w-full sm:w-[180px] h-12 sm:h-10">
              <SelectValue>
                {statusFilter === 'all' ? (
                  <Trans i18nKey="novel2video:status.all" />
                ) : (
                  <Trans i18nKey={`novel2video:status.segment.${statusFilter}`} />
                )}
              </SelectValue>
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">
                <Trans i18nKey="novel2video:status.all" />
              </SelectItem>
              <SelectItem value="pending">
                <Trans i18nKey="novel2video:status.segment.pending" />
              </SelectItem>
              <SelectItem value="processing_image">
                <Trans i18nKey="novel2video:status.segment.processing_image" />
              </SelectItem>
              <SelectItem value="processing_audio">
                <Trans i18nKey="novel2video:status.segment.processing_audio" />
              </SelectItem>
              <SelectItem value="completed">
                <Trans i18nKey="novel2video:status.segment.completed" />
              </SelectItem>
              <SelectItem value="failed">
                <Trans i18nKey="novel2video:status.segment.failed" />
              </SelectItem>
            </SelectContent>
          </Select>
        </motion.div>
      </div>
    </motion.div>
  );
}
