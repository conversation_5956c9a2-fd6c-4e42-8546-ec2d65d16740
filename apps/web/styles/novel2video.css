/* Novel2Video Styles - AI科技感主题样式 */

/* 使用shadcn/ui组件系统，保持主题一致性 */
/* 所有样式都基于CSS变量，支持深色/浅色模式自动切换 */

/* AI-Powered Novel to Video - 科技感界面样式 */

/* AI科技感渐变背景 */
.hero-elegant {
  background: linear-gradient(135deg,
    var(--ai-primary) / 0.05 0%,
    transparent 25%,
    var(--ai-secondary) / 0.03 50%,
    var(--ai-accent) / 0.02 75%,
    transparent 100%);
  position: relative;
}

.hero-elegant::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(ellipse at center,
    var(--ai-glow) 0%,
    transparent 70%);
  pointer-events: none;
}

/* AI主题文字渐变 */
.text-gradient-elegant {
  background: linear-gradient(135deg,
    var(--ai-primary) 0%,
    var(--ai-secondary) 50%,
    var(--ai-accent) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  background-size: 200% 200%;
  animation: gradient-shift 3s ease-in-out infinite;
}

@keyframes gradient-shift {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

/* AI科技感按钮样式 */
.btn-elegant {
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(12px) saturate(150%);
  background: linear-gradient(135deg,
    var(--ai-primary) 0%,
    var(--ai-secondary) 100%);
  border: 1px solid var(--ai-primary) / 0.3;
  box-shadow:
    0 2px 8px var(--ai-glow),
    0 1px 3px var(--ai-primary) / 0.2,
    inset 0 1px 0 var(--ai-primary) / 0.2;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.btn-elegant:hover {
  transform: translateY(-2px) scale(1.02);
  box-shadow:
    0 8px 25px var(--ai-glow),
    0 4px 12px var(--ai-primary) / 0.3,
    inset 0 1px 0 var(--ai-primary) / 0.3;
  background: linear-gradient(135deg,
    var(--ai-secondary) 0%,
    var(--ai-accent) 100%);
}

.btn-elegant:active {
  transform: translateY(-1px) scale(1.01);
}

.btn-elegant::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg,
    transparent,
    var(--ai-accent) / 0.3,
    transparent);
  transition: left 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

.btn-elegant:hover::before {
  left: 100%;
}

/* AI科技感玻璃态卡片 */
.card-elegant {
  position: relative;
  backdrop-filter: blur(20px) saturate(180%);
  background: linear-gradient(135deg,
    hsl(var(--card) / 0.9) 0%,
    hsl(var(--card) / 0.7) 100%);
  border: 1px solid var(--ai-primary) / 0.2;
  box-shadow:
    0 8px 32px var(--ai-glow),
    0 2px 8px var(--ai-primary) / 0.1,
    inset 0 1px 0 var(--ai-primary) / 0.1;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.card-elegant:hover {
  transform: translateY(-4px);
  box-shadow:
    0 12px 40px var(--ai-glow),
    0 4px 16px var(--ai-primary) / 0.2,
    inset 0 1px 0 var(--ai-primary) / 0.2;
  border-color: var(--ai-primary) / 0.4;
}

/* 优雅边框 */
.border-elegant {
  border: 1px solid hsl(var(--border) / 0.3);
  background: linear-gradient(135deg, 
    hsl(var(--card) / 0.8) 0%, 
    hsl(var(--card) / 0.95) 100%);
}

/* 优雅阴影 */
.shadow-elegant {
  box-shadow: 
    0 4px 6px -1px hsl(var(--primary) / 0.1),
    0 2px 4px -1px hsl(var(--primary) / 0.06),
    0 1px 3px 0 hsl(var(--border) / 0.1),
    inset 0 1px 0 hsl(var(--primary) / 0.05);
}

/* AI科技感脉冲动画 */
.ai-pulse {
  animation: ai-pulse 2.5s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes ai-pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
    box-shadow: 0 0 0 0 var(--ai-glow);
  }
  25% {
    opacity: 0.9;
    transform: scale(1.01);
    box-shadow: 0 0 8px 2px var(--ai-glow);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.02);
    box-shadow: 0 0 16px 4px var(--ai-glow);
  }
  75% {
    opacity: 0.9;
    transform: scale(1.01);
    box-shadow: 0 0 8px 2px var(--ai-glow);
  }
}

/* AI光晕效果增强 */
.ai-glow {
  position: relative;
}

.ai-glow::before {
  content: '';
  position: absolute;
  top: -3px;
  left: -3px;
  right: -3px;
  bottom: -3px;
  background: linear-gradient(45deg,
    var(--ai-primary) / 0.4,
    var(--ai-secondary) / 0.4,
    var(--ai-accent) / 0.4,
    var(--ai-primary) / 0.4);
  border-radius: inherit;
  z-index: -1;
  filter: blur(6px);
  opacity: 0;
  transition: opacity 0.4s ease;
  background-size: 200% 200%;
  animation: glow-rotate 3s linear infinite;
}

.ai-glow:hover::before {
  opacity: 1;
}

@keyframes glow-rotate {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

/* AI主题进度条 */
.progress-elegant {
  background: linear-gradient(90deg,
    var(--ai-primary) 0%,
    var(--ai-secondary) 50%,
    var(--ai-accent) 100%);
  box-shadow:
    0 0 12px var(--ai-glow),
    inset 0 1px 0 var(--ai-primary) / 0.3;
  background-size: 200% 100%;
  animation: progress-flow 2s linear infinite;
}

@keyframes progress-flow {
  0% { background-position: -200% 0; }
  100% { background-position: 200% 0; }
}

/* AI状态指示器 */
.status-indicator {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 10px;
  height: 10px;
  border-radius: 50%;
  box-shadow: 0 0 4px currentColor;
}

.status-indicator.processing {
  background: var(--ai-processing);
  animation: ai-processing 2s ease-in-out infinite;
}

.status-indicator.completed {
  background: var(--ai-success);
  animation: ai-success 0.6s ease-out;
}

.status-indicator.failed {
  background: hsl(var(--destructive));
  animation: ai-error 0.8s ease-out;
}

.status-indicator.draft {
  background: hsl(var(--muted-foreground));
  opacity: 0.7;
}

@keyframes ai-processing {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
    box-shadow: 0 0 4px currentColor;
  }
  50% {
    opacity: 0.6;
    transform: scale(1.2);
    box-shadow: 0 0 12px currentColor;
  }
}

@keyframes ai-success {
  0% {
    opacity: 0;
    transform: scale(0.5);
  }
  50% {
    opacity: 1;
    transform: scale(1.3);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes ai-error {
  0%, 100% {
    transform: scale(1);
  }
  25%, 75% {
    transform: scale(1.1);
  }
  50% {
    transform: scale(0.9);
  }
}

/* 响应式设计增强 */
@media (max-width: 768px) {
  .card-elegant {
    backdrop-filter: blur(8px);
  }

  .btn-elegant {
    backdrop-filter: blur(4px);
  }

  /* 移动端项目卡片优化 */
  .ai-data-card {
    margin-bottom: 1rem;
  }

  /* 移动端按钮优化 */
  .ai-data-card .group-hover\:opacity-100 {
    opacity: 1 !important;
  }

  /* 移动端触摸友好的按钮尺寸 */
  .min-h-\[44px\] {
    min-height: 44px;
  }

  /* 移动端文字大小调整 */
  .text-xs {
    font-size: 0.75rem;
  }

  /* 移动端间距优化 */
  .space-x-1 > * + * {
    margin-left: 0.25rem;
  }

  /* 移动端Tab优化 */
  [data-state="active"] {
    background-color: hsl(var(--primary));
    color: hsl(var(--primary-foreground));
  }

  /* 移动端Tab网格布局优化 */
  .grid-cols-3 > [role="tab"] {
    min-height: 48px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 0.25rem;
    padding: 0.5rem;
    font-size: 0.75rem;
    line-height: 1rem;
  }

  /* 移动端Tab激活状态 */
  .grid-cols-3 > [role="tab"][data-state="active"] {
    background-color: hsl(var(--primary));
    color: hsl(var(--primary-foreground));
    font-weight: 600;
  }

  /* 移动端Tab悬停状态 */
  .grid-cols-3 > [role="tab"]:hover:not([data-state="active"]) {
    background-color: hsl(var(--muted));
    color: hsl(var(--foreground));
  }

  /* 移动端TabsList优化 */
  .grid.grid-cols-3.sm\:flex {
    border-radius: 0.5rem;
    background-color: hsl(var(--muted) / 0.5);
    padding: 0.25rem;
  }

  /* 移动端TabsTrigger优化 */
  .grid.grid-cols-3 [data-state="active"] {
    background-color: hsl(var(--background));
    color: hsl(var(--foreground));
    box-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    border-radius: 0.375rem;
  }



  /* 移动端进度条优化 */
  .w-16 {
    width: 4rem;
  }

  /* 移动端项目头部优化 */
  .flex-col.sm\:flex-row {
    gap: 0.75rem;
  }

  /* 移动端项目进度组件优化 */
  .flex-col.sm\:flex-row.sm\:items-center.sm\:justify-between {
    align-items: stretch;
  }

  /* 移动端ElegantProgressStep优化 */
  .flex-col.sm\:flex-row.sm\:items-center {
    align-items: stretch;
  }

  /* 移动端按钮组优化 */
  .flex.items-center.justify-between.sm\:justify-end {
    width: 100%;
  }

  /* 移动端Tab列表优化 */
  .w-full.sm\:w-auto {
    display: flex;
    width: 100%;
  }

  .flex-1.sm\:flex-initial {
    flex: 1;
    justify-content: center;
  }

  /* 移动端卡片间距优化 */
  .grid.gap-4.grid-cols-1.sm\:gap-6 {
    gap: 1rem;
  }

  /* 移动端文本截断优化 */
  .truncate {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  /* 移动端滚动优化 */
  .overflow-y-auto {
    -webkit-overflow-scrolling: touch;
  }

  /* 移动端特殊断点优化 */
  @media (max-width: 640px) {
    /* 超小屏幕优化 */
    .px-4 {
      padding-left: 1rem;
      padding-right: 1rem;
    }

    .py-3 {
      padding-top: 0.75rem;
      padding-bottom: 0.75rem;
    }

    /* 移动端字体大小调整 */
    .text-lg {
      font-size: 1rem;
      line-height: 1.5rem;
    }

    .text-sm {
      font-size: 0.875rem;
      line-height: 1.25rem;
    }

    /* 移动端间距调整 */
    .gap-3 {
      gap: 0.5rem;
    }

    .gap-4 {
      gap: 0.75rem;
    }

    /* 移动端进度条调整 */
    .w-32 {
      width: 6rem;
    }

    /* 移动端按钮调整 */
    .min-h-\[44px\] {
      min-height: 44px;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    /* 移动端片段卡片优化 */
    .line-clamp-3 {
      display: -webkit-box;
      -webkit-line-clamp: 3;
      -webkit-box-orient: vertical;
      overflow: hidden;
    }

    /* 移动端搜索框优化 */
    .h-12 {
      height: 3rem;
    }

    /* 移动端操作按钮优化 */
    .h-8.w-8 {
      height: 2rem;
      width: 2rem;
    }

    /* 移动端卡片内边距优化 */
    .p-3 {
      padding: 0.75rem;
    }

    /* 移动端间距优化 */
    .mb-3 {
      margin-bottom: 0.75rem;
    }

    /* 移动端flex布局优化 */
    .flex-col.sm\:flex-row {
      flex-direction: column;
    }

    .flex-wrap {
      flex-wrap: wrap;
    }
  }

  /* 项目设置移动端优化 */
  @media (max-width: 1024px) {
    /* 移动端项目设置对话框优化 */
    .project-settings-mobile {
      height: 95vh !important;
      max-height: 95vh !important;
      width: 95vw !important;
      max-width: 95vw !important;
    }

    /* 移动端标签栏优化 */
    .project-settings-tabs {
      min-height: 60px;
    }

    /* 移动端内容区域滚动优化 */
    .project-settings-content {
      -webkit-overflow-scrolling: touch;
      overscroll-behavior: contain;
    }

    /* 移动端表单元素优化 */
    .project-settings-form .form-item {
      margin-bottom: 1rem;
    }

    .project-settings-form .form-label {
      font-size: 0.875rem;
      font-weight: 500;
    }

    .project-settings-form .form-control {
      min-height: 44px;
    }

    /* 移动端卡片优化 */
    .project-settings-card {
      margin-bottom: 1rem;
      padding: 1rem;
    }

    .project-settings-card .card-header {
      padding-bottom: 0.75rem;
    }

    .project-settings-card .card-title {
      font-size: 1rem;
    }

    /* 移动端Select组件优化 */
    .project-settings-form [data-radix-select-trigger] {
      min-height: 44px !important;
      font-size: 1rem !important;
      padding: 0.75rem 1rem !important;
      border-radius: 0.5rem !important;
      border: 2px solid hsl(var(--border)) !important;
      background-color: hsl(var(--background)) !important;
    }

    .project-settings-form [data-radix-select-trigger]:focus {
      border-color: hsl(var(--primary)) !important;
      box-shadow: 0 0 0 2px hsl(var(--primary) / 0.2) !important;
    }

    .project-settings-form [data-radix-select-content] {
      max-height: 60vh !important;
      min-width: 280px !important;
      border-radius: 0.5rem !important;
      border: 2px solid hsl(var(--border)) !important;
      background-color: hsl(var(--popover)) !important;
      box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04) !important;
    }

    .project-settings-form [data-radix-select-item] {
      min-height: 44px !important;
      font-size: 1rem !important;
      padding: 0.75rem 1rem !important;
      cursor: pointer;
      border-radius: 0.25rem !important;
      margin: 0.125rem !important;
    }

    .project-settings-form [data-radix-select-item]:hover {
      background-color: hsl(var(--accent)) !important;
    }

    .project-settings-form [data-radix-select-item][data-state="checked"] {
      background-color: hsl(var(--primary)) !important;
      color: hsl(var(--primary-foreground)) !important;
    }

    /* 移动端Switch组件优化 */
    [data-radix-switch-root] {
      width: 44px !important;
      height: 24px !important;
    }

    [data-radix-switch-thumb] {
      width: 20px !important;
      height: 20px !important;
    }

    /* 移动端Slider组件优化 */
    [data-radix-slider-root] {
      height: 20px !important;
    }

    [data-radix-slider-track] {
      height: 6px !important;
    }

    [data-radix-slider-thumb] {
      width: 20px !important;
      height: 20px !important;
    }
  }

  /* 移动端特定优化 */
  @media (max-width: 480px) {
    /* 超小屏幕优化 */
    .px-4 {
      padding-left: 0.75rem;
      padding-right: 0.75rem;
    }

    .py-2 {
      padding-top: 0.5rem;
      padding-bottom: 0.5rem;
    }

    /* 移动端卡片优化 */
    .p-3 {
      padding: 0.5rem;
    }

    /* 移动端字体优化 */
    .text-sm {
      font-size: 0.8rem;
      line-height: 1.2rem;
    }

    .text-xs {
      font-size: 0.7rem;
    }

    /* 移动端按钮优化 */
    .h-8.w-8 {
      height: 2.5rem;
      width: 2.5rem;
    }

    /* 超小屏幕Select组件优化 */
    .project-settings-form [data-radix-select-trigger] {
      min-height: 48px !important;
      font-size: 1.1rem !important;
      padding: 1rem !important;
      border-radius: 0.5rem !important;
      border: 2px solid hsl(var(--border)) !important;
    }

    .project-settings-form [data-radix-select-content] {
      max-height: 50vh !important;
      min-width: calc(100vw - 2rem) !important;
      max-width: calc(100vw - 2rem) !important;
      border-radius: 0.5rem !important;
      border: 2px solid hsl(var(--border)) !important;
    }

    .project-settings-form [data-radix-select-item] {
      min-height: 48px !important;
      font-size: 1.1rem !important;
      padding: 1rem !important;
      line-height: 1.4;
      border-radius: 0.25rem !important;
      margin: 0.125rem !important;
    }

    /* 超小屏幕表单标签优化 */
    .form-label {
      font-size: 1rem !important;
      font-weight: 600 !important;
      margin-bottom: 0.5rem !important;
    }

    /* 超小屏幕输入框优化 */
    .project-settings-form input,
    .project-settings-form textarea {
      min-height: 48px !important;
      font-size: 1.1rem !important;
      padding: 1rem !important;
      border-radius: 0.5rem !important;
      border: 2px solid hsl(var(--border)) !important;
    }

    .project-settings-form input:focus,
    .project-settings-form textarea:focus {
      border-color: hsl(var(--primary)) !important;
      box-shadow: 0 0 0 2px hsl(var(--primary) / 0.2) !important;
    }

    /* 超小屏幕Switch和Slider优化 */
    .project-settings-form [data-radix-switch-root] {
      width: 48px !important;
      height: 28px !important;
    }

    .project-settings-form [data-radix-switch-thumb] {
      width: 24px !important;
      height: 24px !important;
    }

    .project-settings-form [data-radix-slider-thumb] {
      width: 24px !important;
      height: 24px !important;
    }

    /* 移动端表单项间距优化 */
    .project-settings-form .space-y-4 > * + * {
      margin-top: 1.5rem !important;
    }

    .project-settings-form .space-y-6 > * + * {
      margin-top: 2rem !important;
    }
    }

    /* 移动端间距优化 */
    .gap-1 {
      gap: 0.25rem;
    }

    .gap-2 {
      gap: 0.5rem;
    }

    .gap-3 {
      gap: 0.5rem;
    }
  }
}

/* 深色模式优化 */
@media (prefers-color-scheme: dark) {
  .hero-elegant {
    background: linear-gradient(135deg, 
      hsl(var(--primary) / 0.05) 0%, 
      transparent 30%, 
      hsl(var(--secondary) / 0.03) 70%, 
      hsl(var(--accent) / 0.02) 100%);
  }

  .card-elegant {
    background: hsl(var(--card) / 0.9);
    border-color: hsl(var(--border) / 0.7);
  }
}

/* 自定义滚动条样式 */
.custom-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: hsl(var(--primary) / 0.3) transparent;
}

.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: transparent;
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, 
    hsl(var(--primary) / 0.4) 0%, 
    hsl(var(--primary) / 0.6) 50%,
    hsl(var(--accent) / 0.4) 100%);
  border-radius: 3px;
  transition: all 0.2s ease;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, 
    hsl(var(--primary) / 0.6) 0%, 
    hsl(var(--primary) / 0.8) 50%,
    hsl(var(--accent) / 0.6) 100%);
  transform: scaleY(1.1);
}

.custom-scrollbar::-webkit-scrollbar-corner {
  background: transparent;
}

/* 精细滚动条用于小区域 */
.mini-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: hsl(var(--muted-foreground) / 0.3) transparent;
}

.mini-scrollbar::-webkit-scrollbar {
  width: 4px;
  height: 4px;
}

.mini-scrollbar::-webkit-scrollbar-track {
  background: transparent;
}

.mini-scrollbar::-webkit-scrollbar-thumb {
  background: hsl(var(--muted-foreground) / 0.3);
  border-radius: 2px;
  transition: background 0.2s ease;
}

.mini-scrollbar::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--muted-foreground) / 0.5);
}

/* 优雅的滚动容器 */
.scroll-container {
  position: relative;
  overflow: hidden;
}

.scroll-area {
  height: 100%;
  overflow-y: auto;
  padding-right: 4px;
  margin-right: -4px;
}

/* 页面布局优化 */
.page-container {
  height: 100vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.page-header {
  flex-shrink: 0;
}

.page-content {
  flex: 1;
  min-height: 0;
  overflow: hidden;
}

/* 标签页容器优化 */
.tabs-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.tabs-list-container {
  flex-shrink: 0;
  padding: 1rem 1.5rem 0;
  border-bottom: 1px solid hsl(var(--border));
}

.tabs-content {
  flex: 1;
  min-height: 0;
  overflow: hidden;
  padding: 1.5rem;
}

.tabs-scrollable {
  height: 100%;
  overflow-y: auto;
}

/* AI主题增强样式 */

/* AI数据卡片 */
.ai-data-card {
  background: linear-gradient(135deg,
    hsl(var(--card) / 0.95) 0%,
    hsl(var(--card) / 0.9) 100%);
  border: 1px solid var(--ai-primary) / 0.15;
  border-radius: calc(var(--radius) + 2px);
  box-shadow:
    0 4px 16px var(--ai-glow),
    inset 0 1px 0 var(--ai-primary) / 0.1;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.ai-data-card:hover {
  transform: translateY(-2px);
  border-color: var(--ai-primary) / 0.3;
  box-shadow:
    0 8px 24px var(--ai-glow),
    inset 0 1px 0 var(--ai-primary) / 0.2;
}

/* AI徽章样式 */
.ai-badge {
  background: linear-gradient(135deg,
    var(--ai-primary) / 0.1 0%,
    var(--ai-secondary) / 0.1 100%);
  border: 1px solid var(--ai-primary) / 0.3;
  color: var(--ai-primary);
  font-weight: 600;
  letter-spacing: 0.025em;
}

.ai-badge.processing {
  background: linear-gradient(135deg,
    var(--ai-processing) / 0.1 0%,
    var(--ai-secondary) / 0.1 100%);
  border-color: var(--ai-processing) / 0.3;
  color: var(--ai-processing);
  animation: badge-pulse 2s ease-in-out infinite;
}

.ai-badge.success {
  background: linear-gradient(135deg,
    var(--ai-success) / 0.1 0%,
    var(--ai-accent) / 0.1 100%);
  border-color: var(--ai-success) / 0.3;
  color: var(--ai-success);
}

@keyframes badge-pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.05);
  }
}

/* AI输入框增强 */
.ai-input {
  background: linear-gradient(135deg,
    hsl(var(--input) / 0.8) 0%,
    hsl(var(--input) / 0.9) 100%);
  border: 1px solid var(--ai-primary) / 0.2;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.ai-input:focus {
  border-color: var(--ai-primary) / 0.5;
  box-shadow:
    0 0 0 2px var(--ai-glow),
    0 2px 8px var(--ai-primary) / 0.1;
  background: hsl(var(--input));
}

/* AI加载动画 */
.ai-loading {
  position: relative;
  overflow: hidden;
}

.ai-loading::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg,
    transparent 0%,
    var(--ai-primary) / 0.2 50%,
    transparent 100%);
  animation: ai-loading-sweep 2s ease-in-out infinite;
}

@keyframes ai-loading-sweep {
  0% { left: -100%; }
  100% { left: 100%; }
}

/* AI文本高亮 */
.ai-highlight {
  background: linear-gradient(135deg,
    var(--ai-primary) / 0.1 0%,
    var(--ai-secondary) / 0.1 100%);
  color: var(--ai-primary);
  padding: 0.125rem 0.375rem;
  border-radius: calc(var(--radius) - 2px);
  font-weight: 600;
  border: 1px solid var(--ai-primary) / 0.2;
}

/* AI分隔线 */
.ai-divider {
  height: 1px;
  background: linear-gradient(90deg,
    transparent 0%,
    var(--ai-primary) / 0.3 20%,
    var(--ai-secondary) / 0.3 50%,
    var(--ai-accent) / 0.3 80%,
    transparent 100%);
  border: none;
  margin: 1.5rem 0;
}