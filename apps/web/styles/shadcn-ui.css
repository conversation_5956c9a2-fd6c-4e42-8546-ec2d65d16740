/*
* shadcn-ui.css
*
* Update the below to customize your Shadcn UI CSS Colors.
* Refer to https://ui.shadcn.com/themes for applying new colors.
* NB: apply the hsl function to the colors copied from the theme.
 */

@layer base {
    :root {
        --font-sans: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";
        --font-heading: var(--font-sans);

        /* AI科技感配色方案 - 浅色模式 */
        --background: oklch(98.5% 0.002 240);
        --foreground: oklch(15% 0.02 240);

        --card: oklch(100% 0 0);
        --card-foreground: oklch(15% 0.02 240);

        --popover: oklch(100% 0 0);
        --popover-foreground: oklch(15% 0.02 240);

        /* 主色调：深蓝紫渐变，体现AI科技感 */
        --primary: oklch(45% 0.15 260);
        --primary-foreground: oklch(98% 0.01 260);

        /* 次要色：微妙的蓝紫色调 */
        --secondary: oklch(96% 0.02 260);
        --secondary-foreground: oklch(25% 0.08 260);

        /* 静音色：保持优雅的灰色基调 */
        --muted: oklch(96% 0.01 240);
        --muted-foreground: oklch(50% 0.02 240);

        /* 强调色：青蓝色，代表创新和创造力 */
        --accent: oklch(94% 0.04 200);
        --accent-foreground: oklch(25% 0.08 200);

        /* 危险色：保持红色但更现代 */
        --destructive: oklch(55% 0.15 25);
        --destructive-foreground: oklch(98% 0.01 25);

        /* 边框和输入：微妙的蓝色调 */
        --border: oklch(92% 0.01 240);
        --input: oklch(94% 0.01 240);
        --ring: oklch(45% 0.15 260);

        --radius: 0.5rem;

        /* 图表颜色：AI主题色彩 */
        --chart-1: oklch(65% 0.12 260); /* 主蓝紫 */
        --chart-2: oklch(70% 0.10 200); /* 青蓝 */
        --chart-3: oklch(60% 0.08 180); /* 青绿 */
        --chart-4: oklch(75% 0.08 300); /* 紫色 */
        --chart-5: oklch(80% 0.06 320); /* 粉紫 */

        /* 侧边栏：科技感配色 */
        --sidebar-background: oklch(97% 0.01 240);
        --sidebar-foreground: oklch(30% 0.04 240);
        --sidebar-primary: oklch(45% 0.15 260);
        --sidebar-primary-foreground: oklch(98% 0.01 260);
        --sidebar-accent: oklch(94% 0.02 240);
        --sidebar-accent-foreground: oklch(25% 0.06 240);
        --sidebar-border: var(--border);
        --sidebar-ring: oklch(45% 0.15 260);

        /* AI特色变量 */
        --ai-primary: oklch(45% 0.15 260);
        --ai-secondary: oklch(70% 0.10 200);
        --ai-accent: oklch(60% 0.08 180);
        --ai-glow: oklch(45% 0.15 260 / 0.3);
        --ai-success: oklch(60% 0.12 150);
        --ai-warning: oklch(70% 0.15 60);
        --ai-processing: oklch(65% 0.12 260);
    }

    .dark {
        /* AI科技感配色方案 - 深色模式 - 优化版本 */
        /* 主背景：更深的背景，增加层次感 */
        --background: oklch(6% 0.015 240);
        --foreground: oklch(96% 0.005 240);

        /* 卡片：明显区别于背景的层次 */
        --card: oklch(12% 0.02 240);
        --card-foreground: oklch(96% 0.005 240);

        /* 弹出层：更高的层次感 */
        --popover: oklch(14% 0.02 240);
        --popover-foreground: oklch(96% 0.005 240);

        /* 主色调：保持亮蓝紫色 */
        --primary: oklch(70% 0.15 260);
        --primary-foreground: oklch(6% 0.015 240);

        /* 次要色：增加对比度 */
        --secondary: oklch(18% 0.03 260);
        --secondary-foreground: oklch(88% 0.01 260);

        /* 静音色：更好的层次区分 */
        --muted: oklch(16% 0.015 240);
        --muted-foreground: oklch(68% 0.008 240);

        /* 强调色：增加对比度 */
        --accent: oklch(20% 0.04 200);
        --accent-foreground: oklch(88% 0.01 200);

        /* 危险色：保持现代红色 */
        --destructive: oklch(62% 0.15 25);
        --destructive-foreground: oklch(96% 0.005 25);

        /* 边框和输入：更明显的边框 */
        --border: oklch(24% 0.02 240);
        --input: oklch(20% 0.02 240);
        --ring: oklch(70% 0.15 260);

        /* 图表颜色：深色模式AI主题 */
        --chart-1: oklch(70% 0.15 260); /* 亮蓝紫 */
        --chart-2: oklch(75% 0.12 200); /* 亮青蓝 */
        --chart-3: oklch(65% 0.10 180); /* 亮青绿 */
        --chart-4: oklch(80% 0.10 300); /* 亮紫色 */
        --chart-5: oklch(85% 0.08 320); /* 亮粉紫 */

        /* 侧边栏：深色模式科技感 - 增强层次感 */
        --sidebar-background: oklch(9% 0.02 240);
        --sidebar-foreground: oklch(88% 0.008 240);
        --sidebar-primary: oklch(70% 0.15 260);
        --sidebar-primary-foreground: oklch(6% 0.015 240);
        --sidebar-accent: oklch(15% 0.025 240);
        --sidebar-accent-foreground: oklch(88% 0.008 240);
        --sidebar-border: oklch(22% 0.02 240);
        --sidebar-ring: oklch(70% 0.15 260);

        /* AI特色变量 - 深色模式 */
        --ai-primary: oklch(70% 0.15 260);
        --ai-secondary: oklch(75% 0.12 200);
        --ai-accent: oklch(65% 0.10 180);
        --ai-glow: oklch(70% 0.15 260 / 0.4);
        --ai-success: oklch(65% 0.12 150);
        --ai-warning: oklch(75% 0.15 60);
        --ai-processing: oklch(70% 0.15 260);
    }
}

/* 数字变化动画效果 */
@keyframes number-bounce {
  0% {
    transform: scale(1) translateY(0);
    color: hsl(var(--foreground));
  }
  30% {
    transform: scale(1.15) translateY(-3px);
    color: hsl(var(--primary));
  }
  60% {
    transform: scale(1.05) translateY(-1px);
    color: hsl(var(--primary));
  }
  100% {
    transform: scale(1.1) translateY(0);
    color: hsl(var(--primary));
  }
}

@keyframes progress-pulse {
  0% {
    transform: scale(1);
    color: hsl(var(--foreground));
    text-shadow: none;
  }
  25% {
    transform: scale(1.1);
    color: hsl(var(--primary));
    text-shadow: 0 0 8px hsl(var(--primary) / 0.5);
  }
  50% {
    transform: scale(1.15);
    color: hsl(var(--primary));
    text-shadow: 0 0 12px hsl(var(--primary) / 0.7);
  }
  75% {
    transform: scale(1.1);
    color: hsl(var(--primary));
    text-shadow: 0 0 8px hsl(var(--primary) / 0.5);
  }
  100% {
    transform: scale(1.1);
    color: hsl(var(--primary));
    text-shadow: 0 0 4px hsl(var(--primary) / 0.3);
  }
}

@keyframes card-glow {
  0% {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }
  50% {
    box-shadow: 0 4px 20px hsl(var(--primary) / 0.2);
  }
  100% {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }
}

.animate-number-bounce {
  animation: number-bounce 0.8s cubic-bezier(0.34, 1.56, 0.64, 1);
}

.animate-progress-pulse {
  animation: progress-pulse 1.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.animate-card-glow {
  animation: card-glow 0.6s ease-out;
}

/* 进度条动画 */
.progress-bar-animated {
  transition: all 4s cubic-bezier(0.23, 1, 0.32, 1);
}

.progress-bar-animated > div {
  transition: width 4s cubic-bezier(0.23, 1, 0.32, 1) !important;
}

.progress-bar-animated [data-state="complete"] {
  transition: width 4s cubic-bezier(0.23, 1, 0.32, 1) !important;
}

/* 步骤卡片悬停效果 */
.step-card {
  transition: all 0.3s ease;
}

.step-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* 数字更新时的闪烁效果 */
@keyframes flash-update {
  0% {
    background-color: transparent;
  }
  25% {
    background-color: hsl(var(--primary) / 0.1);
  }
  100% {
    background-color: transparent;
  }
}

.number-flash {
  animation: flash-update 0.4s ease-out;
  border-radius: 4px;
  padding: 2px 4px;
  margin: -2px -4px;
}

/* 增强的悬停效果 */
.step-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  border-color: hsl(var(--primary) / 0.3);
}

/* 加载状态动画 */
@keyframes spinner-pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.5;
    transform: scale(0.9);
  }
}

.loading-pulse {
  animation: spinner-pulse 2s ease-in-out infinite;
}

/* 数字计数器效果 */
.counter-digit {
  position: relative;
  display: inline-block;
  overflow: hidden;
}

/* 添加微妙的闪烁效果 */
@keyframes subtle-glow {
  0%, 100% {
    text-shadow: none;
  }
  50% {
    text-shadow: 0 0 8px hsl(var(--primary) / 0.6);
  }
}

.animate-subtle-glow {
  animation: subtle-glow 2s ease-in-out infinite;
}

/* 进度条内的填充动画 */
@keyframes progress-fill {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

.progress-animated::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  background-image: linear-gradient(
    -45deg,
    transparent 33%,
    rgba(255, 255, 255, 0.15) 33%,
    rgba(255, 255, 255, 0.15) 66%,
    transparent 66%
  );
  background-size: 30px 30px;
  animation: progress-fill 4s linear infinite;
}

/* 暗色主题增强样式 */
.dark {
  /* 增强卡片的层次感 */
  .card {
    border: 1px solid oklch(22% 0.02 240);
    box-shadow: 0 1px 3px oklch(4% 0.02 240 / 0.3);
  }

  /* 增强侧边栏的视觉分离 */
  .sidebar {
    border-right: 1px solid oklch(22% 0.02 240);
    box-shadow: 2px 0 8px oklch(4% 0.02 240 / 0.2);
  }

  /* 增强导航栏的层次感 */
  .page-header {
    border-bottom: 1px solid oklch(22% 0.02 240);
    background: oklch(8% 0.018 240);
    backdrop-filter: blur(8px);
  }

  /* 增强下拉菜单的层次感 */
  .dropdown-menu-content {
    border: 1px solid oklch(24% 0.02 240);
    box-shadow: 0 4px 12px oklch(4% 0.02 240 / 0.4);
    background: oklch(14% 0.02 240);
  }

  /* 增强按钮的对比度 */
  .button-secondary {
    background: oklch(18% 0.03 260);
    border: 1px solid oklch(24% 0.02 240);
  }

  .button-secondary:hover {
    background: oklch(22% 0.03 260);
    border-color: oklch(28% 0.02 240);
  }

  /* 增强输入框的视觉效果 */
  .input {
    background: oklch(10% 0.02 240);
    border: 1px solid oklch(24% 0.02 240);
  }

  .input:focus {
    border-color: oklch(70% 0.15 260);
    box-shadow: 0 0 0 2px oklch(70% 0.15 260 / 0.2);
  }

  /* 增强表格的层次感 */
  .table-row {
    border-bottom: 1px solid oklch(20% 0.02 240);
  }

  .table-row:hover {
    background: oklch(10% 0.02 240);
  }

  /* 增强选中状态的视觉效果 */
  .selected {
    background: oklch(15% 0.04 260);
    border-color: oklch(70% 0.15 260);
  }
}