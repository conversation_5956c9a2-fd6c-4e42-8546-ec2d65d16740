{"name": "@kit/ui", "private": true, "version": "0.1.0", "scripts": {"clean": "git clean -xdf .turbo node_modules", "format": "prettier --check \"**/*.{ts,tsx}\"", "lint": "eslint .", "typecheck": "tsc --noEmit"}, "dependencies": {"@hookform/resolvers": "^5.1.1", "@radix-ui/react-accordion": "1.2.11", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "1.1.11", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.14", "@radix-ui/react-tooltip": "1.2.7", "clsx": "^2.1.1", "cmdk": "1.1.1", "framer-motion": "^12.19.2", "input-otp": "1.4.2", "lucide-react": "^0.523.0", "motion": "^12.19.2", "react-dropzone": "^14.3.8", "react-top-loading-bar": "3.0.2", "recharts": "2.15.3", "tailwind-merge": "^3.3.1"}, "devDependencies": {"@kit/eslint-config": "workspace:*", "@kit/prettier-config": "workspace:*", "@kit/tsconfig": "workspace:*", "@radix-ui/react-icons": "^1.3.2", "@supabase/supabase-js": "2.50.2", "@tanstack/react-query": "5.81.2", "@tanstack/react-table": "^8.21.3", "@types/react": "19.1.8", "@types/react-dom": "19.1.6", "class-variance-authority": "^0.7.1", "date-fns": "^4.1.0", "eslint": "^9.29.0", "next": "15.3.4", "next-themes": "0.4.6", "prettier": "^3.6.1", "react-day-picker": "^9.7.0", "react-hook-form": "^7.58.1", "react-i18next": "^15.5.3", "sonner": "^2.0.5", "tailwindcss": "4.1.10", "tailwindcss-animate": "^1.0.7", "typescript": "^5.8.3", "zod": "^3.25.67"}, "prettier": "@kit/prettier-config", "imports": {"#utils": ["./src/lib/utils/index.ts"]}, "exports": {"./accordion": "./src/shadcn/accordion.tsx", "./alert-dialog": "./src/shadcn/alert-dialog.tsx", "./avatar": "./src/shadcn/avatar.tsx", "./button": "./src/shadcn/button.tsx", "./calendar": "./src/shadcn/calendar.tsx", "./card": "./src/shadcn/card.tsx", "./checkbox": "./src/shadcn/checkbox.tsx", "./command": "./src/shadcn/command.tsx", "./data-table": "./src/shadcn/data-table.tsx", "./dialog": "./src/shadcn/dialog.tsx", "./dropdown-menu": "./src/shadcn/dropdown-menu.tsx", "./navigation-menu": "./src/shadcn/navigation-menu.tsx", "./form": "./src/shadcn/form.tsx", "./input": "./src/shadcn/input.tsx", "./label": "./src/shadcn/label.tsx", "./popover": "./src/shadcn/popover.tsx", "./scroll-area": "./src/shadcn/scroll-area.tsx", "./select": "./src/shadcn/select.tsx", "./sheet": "./src/shadcn/sheet.tsx", "./table": "./src/shadcn/table.tsx", "./tabs": "./src/shadcn/tabs.tsx", "./tooltip": "./src/shadcn/tooltip.tsx", "./sonner": "./src/shadcn/sonner.tsx", "./heading": "./src/shadcn/heading.tsx", "./alert": "./src/shadcn/alert.tsx", "./badge": "./src/shadcn/badge.tsx", "./radio-group": "./src/shadcn/radio-group.tsx", "./separator": "./src/shadcn/separator.tsx", "./input-otp": "./src/shadcn/input-otp.tsx", "./textarea": "./src/shadcn/textarea.tsx", "./switch": "./src/shadcn/switch.tsx", "./breadcrumb": "./src/shadcn/breadcrumb.tsx", "./chart": "./src/shadcn/chart.tsx", "./skeleton": "./src/shadcn/skeleton.tsx", "./shadcn-sidebar": "./src/shadcn/sidebar.tsx", "./collapsible": "./src/shadcn/collapsible.tsx", "./utils": "./src/lib/utils/index.ts", "./if": "./src/makerkit/if.tsx", "./trans": "./src/makerkit/trans.tsx", "./sidebar": "./src/makerkit/sidebar.tsx", "./navigation-schema": "./src/makerkit/navigation-config.schema.ts", "./bordered-navigation-menu": "./src/makerkit/bordered-navigation-menu.tsx", "./spinner": "./src/makerkit/spinner.tsx", "./page": "./src/makerkit/page.tsx", "./image-uploader": "./src/makerkit/image-uploader.tsx", "./global-loader": "./src/makerkit/global-loader.tsx", "./auth-change-listener": "./src/makerkit/auth-change-listener.tsx", "./loading-overlay": "./src/makerkit/loading-overlay.tsx", "./profile-avatar": "./src/makerkit/profile-avatar.tsx", "./mode-toggle": "./src/makerkit/mode-toggle.tsx", "./mobile-mode-toggle": "./src/makerkit/mobile-mode-toggle.tsx", "./enhanced-data-table": "./src/makerkit/data-table.tsx", "./language-selector": "./src/makerkit/language-selector.tsx", "./stepper": "./src/makerkit/stepper.tsx", "./cookie-banner": "./src/makerkit/cookie-banner.tsx", "./card-button": "./src/makerkit/card-button.tsx", "./version-updater": "./src/makerkit/version-updater.tsx", "./multi-step-form": "./src/makerkit/multi-step-form.tsx", "./app-breadcrumbs": "./src/makerkit/app-breadcrumbs.tsx", "./empty-state": "./src/makerkit/empty-state.tsx", "./marketing": "./src/makerkit/marketing/index.tsx", "./oauth-provider-logo-image": "./src/makerkit/oauth-provider-logo-image.tsx", "./file-uploader": "./src/makerkit/file-uploader.tsx", "./progress": "./src/shadcn/progress.tsx", "./slider": "./src/shadcn/slider.tsx", "./use-mobile": "./src/hooks/use-mobile.tsx"}, "typesVersions": {"*": {"*": ["src/*"]}}}